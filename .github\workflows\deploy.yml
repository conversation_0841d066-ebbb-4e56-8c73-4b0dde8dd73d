name: Deploy to Production
on:
  push:
    branches:
      - main
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20.10.0"

      # Portfolio Build and Setup
      - name: Install portfolio dependencies
        run: npm ci

      - name: Build the portfolio project
        run: node build-prod.js
        env:
          # Set environment to PROD for GitHub Actions builds
          ENVIRONMENT: PROD
          # Required configuration - will cause build to fail if missing
          REACT_APP_AUTH_SERVER_URL: https://my-next-auth-app-ten.vercel.app
          # Optional configuration - from GitHub repository variables
          REACT_APP_CLIENT_URL: https://vishal.biyani.xyz
          REACT_APP_GITHUB_CLIENT_ID: ${{ vars.REACT_APP_GITHUB_CLIENT_ID }}
          REACT_APP_GOOGLE_CLIENT_ID: ${{ vars.REACT_APP_GOOGLE_CLIENT_ID }}
          REACT_APP_REDIRECT_URI: ${{ vars.REACT_APP_REDIRECT_URI }}
          REACT_APP_TOKEN_PROXY_URL: ${{ vars.REACT_APP_TOKEN_PROXY_URL }}

      # Copy 404.html to build folder for main site
      - name: Copy 404.html for main site
        run: |
          if [ -f "public/404.html" ]; then
            echo "Copying 404.html from public directory"
            cp public/404.html build/
          else
            echo "Creating 404.html in build directory"
            cat > build/404.html <<"EOF"
            <!DOCTYPE html>
            <html>
              <head>
                <meta charset="utf-8" />
                <script type="text/javascript">
                  (function () {
                    const search = window.location.search;
                    const params = new URLSearchParams(search);
                    const redirect = params.get("redirect") || "/";
                    window.location.replace(redirect);
                  })();
                </script>
              </head>
              <body>
                <p>Redirecting... If you are not redirected automatically, <a href="/">click here</a>.</p>
              </body>
            </html>
          EOF
          fi

      # Create docs directory and basic index.html
      - name: Create basic docs index
        run: |
          mkdir -p build/docs
          cat > build/docs/index.html <<"EOF"
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <meta http-equiv="refresh" content="0;url=/docs/">
            <title>Portfolio Documentation</title>
            <style>
              body {
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 20px;
                text-align: center;
              }
              .loading {
                display: inline-block;
                margin-top: 20px;
              }
              .loading:after {
                content: " .";
                animation: dots 1.5s steps(5, end) infinite;
              }
              @keyframes dots {
                0%, 20% { content: " ."; }
                40% { content: " .."; }
                60% { content: " ..."; }
                80%, 100% { content: " ...."; }
              }
            </style>
          </head>
          <body>
            <h1>Portfolio Documentation</h1>
            <p>Loading documentation<span class="loading"></span></p>
            <p>If you are not redirected automatically, <a href="/docs/">click here</a>.</p>
          </body>
          </html>
          EOF

      # Docusaurus Build
      - name: Install Docusaurus dependencies
        run: |
          cd docs-site
          echo "Installing Docusaurus dependencies..."
          npm install

      - name: Build Docusaurus
        run: |
          cd docs-site
          echo "Building Docusaurus site..."
          npm run build

          # Create .nojekyll file to prevent GitHub Pages processing
          touch ./build/.nojekyll

          echo "Docusaurus build completed."

      # Copy Docusaurus build to the main build directory
      - name: Copy Docusaurus build to main build directory
        run: |
          echo "Copying Docusaurus build to main build/docs directory..."

          # Copy all files from docs-site/build to build/docs
          cp -r ./docs-site/build/* ./build/docs/

          # Add diagnostics script
          cat > ./build/docs/diagnostics.js <<"EOF"
          // Diagnostic script to help troubleshoot Docusaurus issues
          console.log('📊 Running diagnostics for Docusaurus site');

          // List all loaded scripts
          const scripts = document.querySelectorAll('script');
          console.log('📜 Loaded scripts:');
          scripts.forEach((script, index) => {
            console.log(`${index + 1}. ${script.src || '(inline script)'}`);
          });

          // Check if key Docusaurus elements exist
          console.log('🔍 Checking for key Docusaurus elements:');
          const navbar = document.querySelector('nav.navbar');
          console.log(`Navbar: ${navbar ? '✅ Found' : '❌ Not found'}`);

          const sidebar = document.querySelector('div.theme-doc-sidebar-container');
          console.log(`Sidebar: ${sidebar ? '✅ Found' : '❌ Not found'}`);

          const mainContent = document.querySelector('main.container');
          console.log(`Main content: ${mainContent ? '✅ Found' : '❌ Not found'}`);

          console.log('📊 Diagnostics complete');
          EOF

          # Add the diagnostics script to the docs index.html
          if [ -f "./build/docs/index.html" ]; then
            echo "Adding diagnostics script to docs/index.html"
            sed -i 's/<\/body>/<script src="diagnostics.js"><\/script><\/body>/' ./build/docs/index.html
          fi

      # Deploy to GitHub Pages
      - name: Deploy to GitHub Pages
        uses: peaceiris/actions-gh-pages@v4
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./build
          cname: vishal.biyani.xyz
