/**
 * Any CSS included here will be global. The classic template
 * bundles Infima by default. Infima is a CSS framework designed to
 * work well for content-centric websites.
 */

/* You can override the default Infima variables here. */
:root {
  --ifm-color-primary: #2e8555;
  --ifm-color-primary-dark: #29784c;
  --ifm-color-primary-darker: #277148;
  --ifm-color-primary-darkest: #205d3b;
  --ifm-color-primary-light: #33925d;
  --ifm-color-primary-lighter: #359962;
  --ifm-color-primary-lightest: #3cad6e;
  --ifm-code-font-size: 95%;
  --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.1);
}

/* For readability concerns, you should choose a lighter palette in dark mode. */
[data-theme="dark"] {
  --ifm-color-primary: #25c2a0;
  --ifm-color-primary-dark: #21af90;
  --ifm-color-primary-darker: #1fa588;
  --ifm-color-primary-darkest: #1a8870;
  --ifm-color-primary-light: #29d5b0;
  --ifm-color-primary-lighter: #32d8b4;
  --ifm-color-primary-lightest: #4fddbf;
  --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.3);
}

/* Navbar styling */
.navbar {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8px);
  height: var(--portfolio-header-height);
}

.navbar__link {
  font-weight: 500;
  transition: all 0.3s ease;
}

.navbar__link:hover {
  opacity: 0.8;
  transform: translateY(-2px);
}

.navbar__brand {
  font-weight: 700;
}

/* Footer styling */
.footer {
  padding: 2rem 0;
  height: var(--portfolio-footer-height);
  border-top: 1px solid var(--ifm-color-emphasis-300);
}

.footer__link-item {
  transition: all 0.3s ease;
}

.footer__link-item:hover {
  text-decoration: underline;
  opacity: 0.8;
}

.footer__copyright {
  font-size: 0.875rem;
  opacity: 0.8;
}

/* Button styling */
.button {
  border-radius: 8px;
  text-transform: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

.button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Card styling */
.card {
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

/* Markdown content styling */
.markdown {
  --ifm-heading-font-weight: 600;
}

.markdown h1 {
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
}

.markdown h2 {
  font-size: 2rem;
  margin-top: 2.5rem;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--ifm-color-emphasis-200);
}

.markdown h3 {
  font-size: 1.5rem;
  margin-top: 2rem;
  margin-bottom: 0.75rem;
}

.markdown code {
  border-radius: 4px;
  padding: 0.2rem 0.4rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .navbar {
    height: 56px;
  }

  .footer {
    padding: 1rem 0;
  }

  .markdown h1 {
    font-size: 2rem;
  }

  .markdown h2 {
    font-size: 1.75rem;
  }

  .markdown h3 {
    font-size: 1.25rem;
  }
}
