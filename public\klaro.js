!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.klaro=t():e.klaro=t()}(self,(()=>(()=>{var e={2690:(e,t,r)=>{"use strict";function n(){for(var e=document.cookie.split(";"),t=[],r=new RegExp("^\\s*([^=]+)\\s*=\\s*(.*?)$"),n=0;n<e.length;n++){var o=e[n],i=r.exec(o);null!==i&&t.push({name:i[1],value:i[2]})}return t}function o(e,t,r){var n=e+"=; Max-Age=-99999999;";document.cookie=n,n+=" path="+(t||"/")+";",document.cookie=n,void 0!==r&&(n+=" domain="+r+";",document.cookie=n)}r.d(t,{default:()=>C}),r(9305),r(2733),r(4701),r(1678),r(4776),r(4382),r(9892),r(4962),r(6584),r(9336),r(4754),r(1908),r(94),r(7132),r(6457),r(8908),r(3810),r(8557),r(646),r(5021),r(3687),r(2745),r(3994),r(3062),r(4062),r(3630),r(2367);var i=r(5482);function a(e){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}function c(e,t,r){return t=l(t),function(e,t){if(t&&("object"===a(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,s()?Reflect.construct(t,r||[],l(e).constructor):t.apply(e,r))}function s(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(s=function(){return!!e})()}function l(e){return l=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},l(e)}function u(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&p(e,t)}function p(e,t){return p=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},p(e,t)}function d(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function f(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,v(n.key),n)}}function m(e,t,r){return t&&f(e.prototype,t),r&&f(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function v(e){var t=function(e,t){if("object"!=a(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,"string");if("object"!=a(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==a(t)?t:String(t)}r(6437),r(2697),r(1359);var y=function(){function e(){d(this,e),this.value=null}return m(e,[{key:"get",value:function(){return this.value}},{key:"set",value:function(e){this.value=e}},{key:"delete",value:function(){this.value=null}}]),e}(),h=function(){function e(t){d(this,e),this.cookieName=t.storageName,this.cookieDomain=t.cookieDomain,this.cookiePath=t.cookiePath,this.cookieExpiresAfterDays=t.cookieExpiresAfterDays}return m(e,[{key:"get",value:function(){var e=function(e){for(var t=n(),r=0;r<t.length;r++)if(t[r].name===e)return t[r];return null}(this.cookieName);return e?e.value:null}},{key:"set",value:function(e){return function(e,t,r,n,o){var i="";if(r){var a=new Date;a.setTime(a.getTime()+24*r*60*60*1e3),i="; expires="+a.toUTCString()}void 0!==n&&(i+="; domain="+n),i+=void 0!==o?"; path="+o:"; path=/",document.cookie=e+"="+(t||"")+i+"; SameSite=Lax"}(this.cookieName,e,this.cookieExpiresAfterDays,this.cookieDomain,this.cookiePath)}},{key:"delete",value:function(){return o(this.cookieName)}}]),e}(),b=function(){function e(t,r){d(this,e),this.key=t.storageName,this.handle=r}return m(e,[{key:"get",value:function(){return this.handle.getItem(this.key)}},{key:"getWithKey",value:function(e){return this.handle.getItem(e)}},{key:"set",value:function(e){return this.handle.setItem(this.key,e)}},{key:"setWithKey",value:function(e,t){return this.handle.setItem(e,t)}},{key:"delete",value:function(){return this.handle.removeItem(this.key)}},{key:"deleteWithKey",value:function(e){return this.handle.removeItem(e)}}]),e}(),g=function(e){function t(e){return d(this,t),c(this,t,[e,localStorage])}return u(t,e),m(t)}(b),k=function(e){function t(e){return d(this,t),c(this,t,[e,sessionStorage])}return u(t,e),m(t)}(b);const _={cookie:h,test:y,localStorage:g,sessionStorage:k};function w(e){return w="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},w(e)}function x(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=j(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,c=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return a=e.done,e},e:function(e){c=!0,i=e},f:function(){try{a||null==r.return||r.return()}finally{if(c)throw i}}}}function S(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],s=!0,l=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=i.call(r)).done)&&(c.push(n.value),c.length!==t);s=!0);}catch(e){l=!0,o=e}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(e,t)||j(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function j(e,t){if(e){if("string"==typeof e)return O(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?O(e,t):void 0}}function O(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function E(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function P(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?E(Object(r),!0).forEach((function(t){var n,o,i;n=e,o=t,i=r[t],(o=z(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):E(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function A(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,z(n.key),n)}}function z(e){var t=function(e,t){if("object"!=w(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,"string");if("object"!=w(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==w(t)?t:String(t)}var C=function(){function e(t,r,n){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.config=t,this.store=void 0!==r?r:new _[this.storageMethod](this),void 0===this.store&&(this.store=_.cookie),this.auxiliaryStore=void 0!==n?n:new k(this),this.consents=this.defaultConsents,this.confirmed=!1,this.changed=!1,this.states={},this.initialized={},this.executedOnce={},this.watchers=new Set([]),this.loadConsents(),this.applyConsents(),this.savedConsents=P({},this.consents)}var t,r;return t=e,(r=[{key:"storageMethod",get:function(){return this.config.storageMethod||"cookie"}},{key:"storageName",get:function(){return this.config.storageName||this.config.cookieName||"klaro"}},{key:"cookieDomain",get:function(){return this.config.cookieDomain||void 0}},{key:"cookiePath",get:function(){return this.config.cookiePath||void 0}},{key:"cookieExpiresAfterDays",get:function(){return this.config.cookieExpiresAfterDays||120}},{key:"defaultConsents",get:function(){for(var e={},t=0;t<this.config.services.length;t++){var r=this.config.services[t];e[r.name]=this.getDefaultConsent(r)}return e}},{key:"watch",value:function(e){this.watchers.has(e)||this.watchers.add(e)}},{key:"unwatch",value:function(e){this.watchers.has(e)&&this.watchers.delete(e)}},{key:"notify",value:function(e,t){var r=this;this.watchers.forEach((function(n){n.update(r,e,t)}))}},{key:"getService",value:function(e){var t=this.config.services.filter((function(t){return t.name===e}));if(t.length>0)return t[0]}},{key:"getDefaultConsent",value:function(e){var t=e.default||e.required;return void 0===t&&(t=this.config.default),void 0===t&&(t=!1),t}},{key:"changeAll",value:function(e){var t=this,r=0;return this.config.services.filter((function(e){return!e.contextualConsentOnly})).map((function(n){n.required||t.config.required||e?t.updateConsent(n.name,!0)&&r++:t.updateConsent(n.name,!1)&&r++})),r}},{key:"updateConsent",value:function(e,t){var r=(this.consents[e]||!1)!==t;return this.consents[e]=t,this.notify("consents",this.consents),r}},{key:"resetConsents",value:function(){this.consents=this.defaultConsents,this.states={},this.confirmed=!1,this.applyConsents(),this.savedConsents=P({},this.consents),this.store.delete(),this.notify("consents",this.consents)}},{key:"getConsent",value:function(e){return this.consents[e]||!1}},{key:"loadConsents",value:function(){var e=this.store.get();return null!==e&&(this.consents=JSON.parse(decodeURIComponent(e)),this._checkConsents(),this.notify("consents",this.consents)),this.consents}},{key:"saveAndApplyConsents",value:function(e){this.saveConsents(e),this.applyConsents()}},{key:"changedConsents",value:function(){for(var e={},t=0,r=Object.entries(this.consents);t<r.length;t++){var n=S(r[t],2),o=n[0],i=n[1];this.savedConsents[o]!==i&&(e[o]=i)}return e}},{key:"saveConsents",value:function(e){var t=encodeURIComponent(JSON.stringify(this.consents));this.store.set(t),this.confirmed=!0,this.changed=!1;var r=this.changedConsents();this.savedConsents=P({},this.consents),this.notify("saveConsents",{changes:r,consents:this.consents,type:e||"script"})}},{key:"applyConsents",value:function(e,t,r){function n(e,t){if(void 0!==e)return("function"==typeof e?e:new Function("opts",e))(t)}for(var o=0,i=0;i<this.config.services.length;i++){var a=this.config.services[i];if(void 0===r||r===a.name){var c=a.vars||{},s={service:a,config:this.config,vars:c};this.initialized[a.name]||(this.initialized[a.name]=!0,n(a.onInit,s))}}for(var l=0;l<this.config.services.length;l++){var u=this.config.services[l];if(void 0===r||r===u.name){var p=this.states[u.name],d=u.vars||{},f=void 0!==u.optOut?u.optOut:this.config.optOut||!1,m=void 0!==u.required?u.required:this.config.required||!1,v=this.confirmed||f||e||t,y=this.getConsent(u.name)&&v||m,h={service:u,config:this.config,vars:d,consents:this.consents,confirmed:this.confirmed};p!==y&&o++,e||(n(y?u.onAccept:u.onDecline,h),this.updateServiceElements(u,y),this.updateServiceStorage(u,y),void 0!==u.callback&&u.callback(y,u),void 0!==this.config.callback&&this.config.callback(y,u),this.states[u.name]=y)}}return this.notify("applyConsents",o,r),o}},{key:"updateServiceElements",value:function(e,t){if(t){if(e.onlyOnce&&this.executedOnce[e.name])return;this.executedOnce[e.name]=!0}for(var r=document.querySelectorAll("[data-name='"+e.name+"']"),n=0;n<r.length;n++){var o=r[n],a=o.parentElement,c=(0,i.RT)(o),s=c.type,l=c.src,u=c.href,p=["href","src","type"];if("placeholder"!==s)if("IFRAME"===o.tagName){if(t&&o.src===l){console.debug("Skipping ".concat(o.tagName," for service ").concat(e.name,", as it already has the correct type..."));continue}var d,f=document.createElement(o.tagName),m=x(o.attributes);try{for(m.s();!(d=m.n()).done;){var v=d.value;f.setAttribute(v.name,v.value)}}catch(e){m.e(e)}finally{m.f()}f.innerText=o.innerText,f.text=o.text,t?(void 0!==c["original-display"]&&(f.style.display=c["original-display"]),void 0!==c.src&&(f.src=c.src)):(f.src="",void 0!==c["modified-by-klaro"]&&void 0!==c["original-display"]?f.setAttribute("data-original-display",c["original-display"]):(void 0!==o.style.display&&f.setAttribute("data-original-display",o.style.display),f.setAttribute("data-modified-by-klaro","yes")),f.style.display="none"),a.insertBefore(f,o),a.removeChild(o)}else if("SCRIPT"===o.tagName||"LINK"===o.tagName){if(t&&o.type===(s||"")&&o.src===l){console.debug("Skipping ".concat(o.tagName," for service ").concat(e.name,", as it already has the correct type or src..."));continue}var y,h=document.createElement(o.tagName),b=x(o.attributes);try{for(b.s();!(y=b.n()).done;){var g=y.value;h.setAttribute(g.name,g.value)}}catch(e){b.e(e)}finally{b.f()}h.innerText=o.innerText,h.text=o.text,t?(h.type=s||"",void 0!==l&&(h.src=l),void 0!==u&&(h.href=u)):h.type="text/plain",a.insertBefore(h,o),a.removeChild(o)}else{if(t){var k,_=x(p);try{for(_.s();!(k=_.n()).done;){var w=k.value,S=c[w];void 0!==S&&(void 0===c["original-"+w]&&(c["original-"+w]=o[w]),o[w]=S)}}catch(e){_.e(e)}finally{_.f()}void 0!==c.title&&(o.title=c.title),void 0!==c["original-display"]?o.style.display=c["original-display"]:o.style.removeProperty("display")}else{void 0!==c.title&&o.removeAttribute("title"),void 0===c["original-display"]&&void 0!==o.style.display&&(c["original-display"]=o.style.display),o.style.display="none";var j,O=x(p);try{for(O.s();!(j=O.n()).done;){var E=j.value;void 0!==c[E]&&(void 0!==c["original-"+E]?o[E]=c["original-"+E]:o.removeAttribute(E))}}catch(e){O.e(e)}finally{O.f()}}(0,i.X7)(c,o)}else t?(o.style.display="none",c["original-display"]=o.style.display):o.style.display=c["original-display"]||"block"}}},{key:"updateServiceStorage",value:function(e,t){if(!t&&void 0!==e.cookies&&e.cookies.length>0)for(var r=n(),i=0;i<e.cookies.length;i++){var a=e.cookies[i],c=void 0,s=void 0;if(a instanceof Array){var l=S(a,3);a=l[0],c=l[1],s=l[2]}else if(a instanceof Object&&!(a instanceof RegExp)){var u=a;a=u.pattern,c=u.path,s=u.domain}if(void 0!==a){a instanceof RegExp||(a=a.startsWith("^")?new RegExp(a):new RegExp("^"+a.replace(/[-[\]/{}()*+?.\\^$|]/g,"\\$&")+"$"));for(var p=0;p<r.length;p++){var d=r[p];null!==a.exec(d.name)&&(console.debug("Deleting cookie:",d.name,"Matched pattern:",a,"Path:",c,"Domain:",s),o(d.name,c,s),void 0===s&&o(d.name,c,"."+window.location.hostname))}}}}},{key:"_checkConsents",value:function(){for(var e=!0,t=new Set(this.config.services.map((function(e){return e.name}))),r=new Set(Object.keys(this.consents)),n=0,o=Object.keys(this.consents);n<o.length;n++){var i=o[n];t.has(i)||delete this.consents[i]}var a,c=x(this.config.services);try{for(c.s();!(a=c.n()).done;){var s=a.value;r.has(s.name)||(this.consents[s.name]=this.getDefaultConsent(s),e=!1)}}catch(e){c.e(e)}finally{c.f()}this.confirmed=e,e||(this.changed=!0)}}])&&A(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),e}()},5482:(e,t,r)=>{"use strict";function n(e,t){if(e){if("string"==typeof e)return o(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?o(e,t):void 0}}function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function i(e){if(null!==document.currentScript&&void 0!==document.currentScript)return document.currentScript;for(var t=document.getElementsByTagName("script"),r=0;r<t.length;r++){var n=t[r];if(n.src.includes(e))return n}return null}function a(e){for(var t={},r=0;r<e.attributes.length;r++){var n=e.attributes[r];n.name.startsWith("data-")&&(t[n.name.slice(5)]=n.value)}return t}function c(e,t){for(var r=Object.keys(e),n=0;n<r.length;n++){var o=r[n],i=e[o];t[o]!==i&&t.setAttribute("data-"+o,i)}}function s(e){var t,r=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=n(e))){r&&(e=r);var o=0,i=function(){};return{s:i,n:function(){return o>=e.length?{done:!0}:{done:!1,value:e[o++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,c=!0,s=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return c=e.done,e},e:function(e){s=!0,a=e},f:function(){try{c||null==r.return||r.return()}finally{if(s)throw a}}}}(document.querySelectorAll("style[data-context=klaro-styles]"));try{for(r.s();!(t=r.n()).done;){var o=t.value,i=o.innerText;void 0!==o.styleSheet&&(i=o.styleSheet.cssText);for(var a=function(){var e,t,r=(e=s[c],t=2,function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],s=!0,l=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=i.call(r)).done)&&(c.push(n.value),c.length!==t);s=!0);}catch(e){l=!0,o=e}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(e,t)||n(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),o=r[0],a=r[1],l=new RegExp("([a-z0-9-]+):[^;]+;[\\s\\n]*\\1:\\s*var\\(--"+o+",\\s*[^\\)]+\\)","g");i=i.replace(l,(function(e,t){return"".concat(t,": ").concat(a,"; ").concat(t,": var(--").concat(o,", ").concat(a,")")}))},c=0,s=Object.entries(e);c<s.length;c++)a();var l=document.createElement("style");l.setAttribute("type","text/css"),l.setAttribute("data-context","klaro-styles"),void 0!==l.styleSheet?l.styleSheet.cssText=i:l.innerText=i,o.parentElement.appendChild(l),o.parentElement.removeChild(o)}}catch(e){r.e(e)}finally{r.f()}}r.d(t,{N3:()=>s,RT:()=>a,X7:()=>c,XZ:()=>i}),r(9305),r(2733),r(4701),r(4776),r(9892),r(6281),r(4962),r(9336),r(1908),r(7132),r(3810),r(8557),r(646),r(5021),r(3687),r(9425),r(3994),r(3062),r(4062),r(2367)},679:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var n=r(8645),o=r.n(n),i=r(278),a=r.n(i)()(o());a.push([e.id,'.klaro{font-family:inherit;font-family:var(--font-family, inherit);font-size:14px;font-size:var(--font-size, 14px)}.klaro button{font-family:inherit;font-family:var(--font-family, inherit);font-size:14px;font-size:var(--font-size, 14px)}.klaro.cm-as-context-notice{height:100%;padding-bottom:12px;padding-top:12px}.klaro .cookie-modal .cm-switch-container,.klaro .context-notice .cm-switch-container,.klaro .cookie-notice .cm-switch-container{border-bottom-style:solid;border-bottom-style:var(--border-style, solid);border-bottom-width:1px;border-bottom-width:var(--border-width, 1px);border-bottom-color:#c8c8c8;border-bottom-color:var(--light2, #c8c8c8);display:block;position:relative;padding:10px;padding-left:66px;line-height:20px;vertical-align:middle;min-height:40px}.klaro .cookie-modal .cm-switch-container:last-child,.klaro .context-notice .cm-switch-container:last-child,.klaro .cookie-notice .cm-switch-container:last-child{border-bottom:0}.klaro .cookie-modal .cm-switch-container:first-child,.klaro .context-notice .cm-switch-container:first-child,.klaro .cookie-notice .cm-switch-container:first-child{margin-top:0}.klaro .cookie-modal .cm-switch-container p,.klaro .context-notice .cm-switch-container p,.klaro .cookie-notice .cm-switch-container p{margin-top:0}.klaro .cookie-modal .cm-switch,.klaro .context-notice .cm-switch,.klaro .cookie-notice .cm-switch{position:relative;display:inline-block;width:50px;height:30px}.klaro .cookie-modal .cm-list-input:checked+.cm-list-label .slider,.klaro .context-notice .cm-list-input:checked+.cm-list-label .slider,.klaro .cookie-notice .cm-list-input:checked+.cm-list-label .slider{background-color:#1a936f;background-color:var(--green1, #1a936f)}.klaro .cookie-modal .cm-list-input.half-checked:checked+.cm-list-label .slider,.klaro .context-notice .cm-list-input.half-checked:checked+.cm-list-label .slider,.klaro .cookie-notice .cm-list-input.half-checked:checked+.cm-list-label .slider{background-color:#1a936f;background-color:var(--green1, #1a936f);opacity:.6}.klaro .cookie-modal .cm-list-input.half-checked:checked+.cm-list-label .slider::before,.klaro .context-notice .cm-list-input.half-checked:checked+.cm-list-label .slider::before,.klaro .cookie-notice .cm-list-input.half-checked:checked+.cm-list-label .slider::before{-ms-transform:translateX(10px);transform:translateX(10px)}.klaro .cookie-modal .cm-list-input.only-required+.cm-list-label .slider,.klaro .context-notice .cm-list-input.only-required+.cm-list-label .slider,.klaro .cookie-notice .cm-list-input.only-required+.cm-list-label .slider{background-color:#24cc9a;background-color:var(--green2, #24cc9a);opacity:.8}.klaro .cookie-modal .cm-list-input.only-required+.cm-list-label .slider::before,.klaro .context-notice .cm-list-input.only-required+.cm-list-label .slider::before,.klaro .cookie-notice .cm-list-input.only-required+.cm-list-label .slider::before{-ms-transform:translateX(10px);transform:translateX(10px)}.klaro .cookie-modal .cm-list-input.required:checked+.cm-list-label .slider,.klaro .context-notice .cm-list-input.required:checked+.cm-list-label .slider,.klaro .cookie-notice .cm-list-input.required:checked+.cm-list-label .slider{background-color:#24cc9a;background-color:var(--green2, #24cc9a);opacity:.8;cursor:not-allowed}.klaro .cookie-modal .slider,.klaro .context-notice .slider,.klaro .cookie-notice .slider{box-shadow:0 4px 6px 0 rgba(0,0,0,.2),5px 5px 10px 0 rgba(0,0,0,.19)}.klaro .cookie-modal .cm-list-input,.klaro .context-notice .cm-list-input,.klaro .cookie-notice .cm-list-input{position:absolute;top:0;left:0;opacity:0;width:50px;height:30px}.klaro .cookie-modal .cm-list-title,.klaro .context-notice .cm-list-title,.klaro .cookie-notice .cm-list-title{font-size:.9em;font-weight:600}.klaro .cookie-modal .cm-list-description,.klaro .context-notice .cm-list-description,.klaro .cookie-notice .cm-list-description{color:#7c7c7c;color:var(--dark3, #7c7c7c);font-size:.9em;padding-top:4px}.klaro .cookie-modal .cm-list-label .cm-switch,.klaro .context-notice .cm-list-label .cm-switch,.klaro .cookie-notice .cm-list-label .cm-switch{position:absolute;left:0}.klaro .cookie-modal .cm-list-label .slider,.klaro .context-notice .cm-list-label .slider,.klaro .cookie-notice .cm-list-label .slider{background-color:#f2f2f2;background-color:var(--white2, #f2f2f2);position:absolute;cursor:pointer;top:0;left:0;right:0;bottom:0;transition:.4s;width:50px;display:inline-block}.klaro .cookie-modal .cm-list-label .slider::before,.klaro .context-notice .cm-list-label .slider::before,.klaro .cookie-notice .cm-list-label .slider::before{background-color:#e6e6e6;background-color:var(--white3, #e6e6e6);position:absolute;content:"";height:20px;width:20px;left:5px;bottom:5px;transition:.4s}.klaro .cookie-modal .cm-list-label .slider.round,.klaro .context-notice .cm-list-label .slider.round,.klaro .cookie-notice .cm-list-label .slider.round{border-radius:30px}.klaro .cookie-modal .cm-list-label .slider.round::before,.klaro .context-notice .cm-list-label .slider.round::before,.klaro .cookie-notice .cm-list-label .slider.round::before{border-radius:50%}.klaro .cookie-modal .cm-list-label input:focus+.slider,.klaro .context-notice .cm-list-label input:focus+.slider,.klaro .cookie-notice .cm-list-label input:focus+.slider{box-shadow-color:#48dfb2;box-shadow-color:var(--green3, #48dfb2);box-shadow:0 0 1px var(--box-shadow-color, green3)}.klaro .cookie-modal .cm-list-label input:checked+.slider::before,.klaro .context-notice .cm-list-label input:checked+.slider::before,.klaro .cookie-notice .cm-list-label input:checked+.slider::before{-ms-transform:translateX(20px);transform:translateX(20px)}.klaro .cookie-modal .cm-list-input:focus+.cm-list-label .slider,.klaro .context-notice .cm-list-input:focus+.cm-list-label .slider,.klaro .cookie-notice .cm-list-input:focus+.cm-list-label .slider{box-shadow:0 4px 6px 0 rgba(125,125,125,.2),5px 5px 10px 0 rgba(125,125,125,.19)}.klaro .cookie-modal .cm-list-input:checked+.cm-list-label .slider::before,.klaro .context-notice .cm-list-input:checked+.cm-list-label .slider::before,.klaro .cookie-notice .cm-list-input:checked+.cm-list-label .slider::before{-ms-transform:translateX(20px);transform:translateX(20px)}.klaro .cookie-modal .slider,.klaro .context-notice .slider,.klaro .cookie-notice .slider{box-shadow:0 4px 6px 0 rgba(0,0,0,.2),5px 5px 10px 0 rgba(0,0,0,.19)}.klaro .cookie-modal a,.klaro .context-notice a,.klaro .cookie-notice a{color:#1a936f;color:var(--green1, #1a936f);text-decoration:none}.klaro .cookie-modal p,.klaro .cookie-modal strong,.klaro .cookie-modal h1,.klaro .cookie-modal h2,.klaro .cookie-modal ul,.klaro .cookie-modal li,.klaro .context-notice p,.klaro .context-notice strong,.klaro .context-notice h1,.klaro .context-notice h2,.klaro .context-notice ul,.klaro .context-notice li,.klaro .cookie-notice p,.klaro .cookie-notice strong,.klaro .cookie-notice h1,.klaro .cookie-notice h2,.klaro .cookie-notice ul,.klaro .cookie-notice li{color:#fafafa;color:var(--light1, #fafafa)}.klaro .cookie-modal p,.klaro .cookie-modal h1,.klaro .cookie-modal h2,.klaro .cookie-modal ul,.klaro .cookie-modal li,.klaro .context-notice p,.klaro .context-notice h1,.klaro .context-notice h2,.klaro .context-notice ul,.klaro .context-notice li,.klaro .cookie-notice p,.klaro .cookie-notice h1,.klaro .cookie-notice h2,.klaro .cookie-notice ul,.klaro .cookie-notice li{display:block;text-align:left;margin:0;padding:0;margin-top:.7em}.klaro .cookie-modal h1,.klaro .cookie-modal h2,.klaro .cookie-modal h3,.klaro .cookie-modal h4,.klaro .cookie-modal h5,.klaro .cookie-modal h6,.klaro .context-notice h1,.klaro .context-notice h2,.klaro .context-notice h3,.klaro .context-notice h4,.klaro .context-notice h5,.klaro .context-notice h6,.klaro .cookie-notice h1,.klaro .cookie-notice h2,.klaro .cookie-notice h3,.klaro .cookie-notice h4,.klaro .cookie-notice h5,.klaro .cookie-notice h6{font-family:inherit;font-family:var(--title-font-family, inherit)}.klaro .cookie-modal .cm-link,.klaro .context-notice .cm-link,.klaro .cookie-notice .cm-link{margin-right:.5em;vertical-align:middle}.klaro .cookie-modal .cm-btn,.klaro .context-notice .cm-btn,.klaro .cookie-notice .cm-btn{color:#fff;color:var(--button-text-color, #fff);background-color:#5c5c5c;background-color:var(--dark2, #5c5c5c);border-radius:4px;border-radius:var(--border-radius, 4px);padding:6px 10px;margin-right:.5em;border-style:none;padding:.4em;font-size:1em;cursor:pointer}.klaro .cookie-modal .cm-btn:disabled,.klaro .context-notice .cm-btn:disabled,.klaro .cookie-notice .cm-btn:disabled{opacity:.5}.klaro .cookie-modal .cm-btn.cm-btn-close,.klaro .context-notice .cm-btn.cm-btn-close,.klaro .cookie-notice .cm-btn.cm-btn-close{background-color:#c8c8c8;background-color:var(--light2, #c8c8c8)}.klaro .cookie-modal .cm-btn.cm-btn-success,.klaro .context-notice .cm-btn.cm-btn-success,.klaro .cookie-notice .cm-btn.cm-btn-success{background-color:#1a936f;background-color:var(--green1, #1a936f)}.klaro .cookie-modal .cm-btn.cm-btn-success-var,.klaro .context-notice .cm-btn.cm-btn-success-var,.klaro .cookie-notice .cm-btn.cm-btn-success-var{background-color:#24cc9a;background-color:var(--green2, #24cc9a)}.klaro .cookie-modal .cm-btn.cm-btn-info,.klaro .context-notice .cm-btn.cm-btn-info,.klaro .cookie-notice .cm-btn.cm-btn-info{background-color:#2581c4;background-color:var(--blue1, #2581c4)}.klaro .context-notice{border-radius:4px;border-radius:var(--border-radius, 4px);border-style:solid;border-style:var(--border-style, solid);border-width:1px;border-width:var(--border-width, 1px);border-color:#c8c8c8;border-color:var(--light2, #c8c8c8);background-color:#fafafa;background-color:var(--light1, #fafafa);display:flex;flex-direction:column;flex-wrap:wrap;align-items:center;justify-content:center;padding:12px;height:100%}.klaro .context-notice.cm-dark{background-color:#333;background-color:var(--dark1, #333);border-color:#5c5c5c;border-color:var(--dark2, #5c5c5c)}.klaro .context-notice.cm-dark p{color:#fafafa;color:var(--light1, #fafafa)}.klaro .context-notice.cm-dark p a{color:#459cdc;color:var(--blue2, #459cdc)}.klaro .context-notice p{color:#333;color:var(--dark1, #333);flex-grow:0;text-align:center;padding-top:0;margin-top:0}.klaro .context-notice p a{color:#24cc9a;color:var(--green2, #24cc9a)}.klaro .context-notice p.cm-buttons{margin-top:12px}.klaro .context-notice p.ccn-description-empty-store{margin-top:24px;font-size:14px;font-size:var(--font-size, 14px)}.klaro .cookie-modal{width:100%;height:100%;position:fixed;overflow:hidden;left:0;top:0;z-index:1000}.klaro .cookie-modal.cm-embedded{position:relative;height:inherit;width:inherit;left:inherit;right:inherit;z-index:0}.klaro .cookie-modal.cm-embedded .cm-modal.cm-klaro{position:relative;-ms-transform:none;transform:none}.klaro .cookie-modal .cm-bg{background:rgba(0,0,0,.5);height:100%;width:100%;position:fixed;top:0;left:0}.klaro .cookie-modal .cm-modal.cm-klaro{background-color:#333;background-color:var(--dark1, #333);color:#fafafa;color:var(--light1, #fafafa);z-index:1001;box-shadow:0 4px 6px 0 rgba(0,0,0,.2),5px 5px 10px 0 rgba(0,0,0,.19);width:100%;max-height:98%;top:50%;-ms-transform:translateY(-50%);transform:translateY(-50%);position:fixed;overflow:auto}@media(min-width: 660px){.klaro .cookie-modal .cm-modal.cm-klaro{border-radius:4px;border-radius:var(--border-radius, 4px);position:relative;margin:0 auto;max-width:640px;height:auto;width:auto}}.klaro .cookie-modal .cm-modal .hide{border-style:none;background:none;cursor:pointer;position:absolute;top:20px;right:20px;z-index:1}.klaro .cookie-modal .cm-modal .hide svg{stroke:#fafafa;stroke:var(--light1, #fafafa)}.klaro .cookie-modal .cm-modal .cm-footer{border-top-color:#5c5c5c;border-top-color:var(--dark2, #5c5c5c);border-top-width:1px;border-top-width:var(--border-width, 1px);border-top-style:solid;border-top-style:var(--border-style, solid);padding:1em}.klaro .cookie-modal .cm-modal .cm-footer-buttons{display:flex;flex-flow:row;justify-content:space-between}.klaro .cookie-modal .cm-modal .cm-footer .cm-powered-by{font-size:.8em;padding-top:4px;text-align:right;padding-right:8px}.klaro .cookie-modal .cm-modal .cm-footer .cm-powered-by a{color:#5c5c5c;color:var(--dark2, #5c5c5c)}.klaro .cookie-modal .cm-modal .cm-header{border-bottom-width:1px;border-bottom-width:var(--border-width, 1px);border-bottom-style:solid;border-bottom-style:var(--border-style, solid);border-bottom-color:#5c5c5c;border-bottom-color:var(--dark2, #5c5c5c);padding:1em;padding-right:24px}.klaro .cookie-modal .cm-modal .cm-header h1{margin:0;font-size:2em;display:block}.klaro .cookie-modal .cm-modal .cm-header h1.title{padding-right:20px}.klaro .cookie-modal .cm-modal .cm-body{padding:1em}.klaro .cookie-modal .cm-modal .cm-body ul{display:block}.klaro .cookie-modal .cm-modal .cm-body span{display:inline-block;width:auto}.klaro .cookie-modal .cm-modal .cm-body ul.cm-services,.klaro .cookie-modal .cm-modal .cm-body ul.cm-purposes{padding:0;margin:0}.klaro .cookie-modal .cm-modal .cm-body ul.cm-services li.cm-purpose .cm-services .cm-caret,.klaro .cookie-modal .cm-modal .cm-body ul.cm-purposes li.cm-purpose .cm-services .cm-caret{color:#a0a0a0;color:var(--light3, #a0a0a0)}.klaro .cookie-modal .cm-modal .cm-body ul.cm-services li.cm-purpose .cm-services .cm-content,.klaro .cookie-modal .cm-modal .cm-body ul.cm-purposes li.cm-purpose .cm-services .cm-content{margin-left:-40px;display:none}.klaro .cookie-modal .cm-modal .cm-body ul.cm-services li.cm-purpose .cm-services .cm-content.expanded,.klaro .cookie-modal .cm-modal .cm-body ul.cm-purposes li.cm-purpose .cm-services .cm-content.expanded{margin-top:10px;display:block}.klaro .cookie-modal .cm-modal .cm-body ul.cm-services li.cm-service,.klaro .cookie-modal .cm-modal .cm-body ul.cm-services li.cm-purpose,.klaro .cookie-modal .cm-modal .cm-body ul.cm-purposes li.cm-service,.klaro .cookie-modal .cm-modal .cm-body ul.cm-purposes li.cm-purpose{position:relative;line-height:20px;vertical-align:middle;padding-left:60px;min-height:40px}.klaro .cookie-modal .cm-modal .cm-body ul.cm-services li.cm-service:first-child,.klaro .cookie-modal .cm-modal .cm-body ul.cm-services li.cm-purpose:first-child,.klaro .cookie-modal .cm-modal .cm-body ul.cm-purposes li.cm-service:first-child,.klaro .cookie-modal .cm-modal .cm-body ul.cm-purposes li.cm-purpose:first-child{margin-top:0}.klaro .cookie-modal .cm-modal .cm-body ul.cm-services li.cm-service p,.klaro .cookie-modal .cm-modal .cm-body ul.cm-services li.cm-purpose p,.klaro .cookie-modal .cm-modal .cm-body ul.cm-purposes li.cm-service p,.klaro .cookie-modal .cm-modal .cm-body ul.cm-purposes li.cm-purpose p{margin-top:0}.klaro .cookie-modal .cm-modal .cm-body ul.cm-services li.cm-service p.purposes,.klaro .cookie-modal .cm-modal .cm-body ul.cm-services li.cm-purpose p.purposes,.klaro .cookie-modal .cm-modal .cm-body ul.cm-purposes li.cm-service p.purposes,.klaro .cookie-modal .cm-modal .cm-body ul.cm-purposes li.cm-purpose p.purposes{color:#a0a0a0;color:var(--light3, #a0a0a0);font-size:.8em}.klaro .cookie-modal .cm-modal .cm-body ul.cm-services li.cm-service.cm-toggle-all,.klaro .cookie-modal .cm-modal .cm-body ul.cm-services li.cm-purpose.cm-toggle-all,.klaro .cookie-modal .cm-modal .cm-body ul.cm-purposes li.cm-service.cm-toggle-all,.klaro .cookie-modal .cm-modal .cm-body ul.cm-purposes li.cm-purpose.cm-toggle-all{border-top-width:1px;border-top-width:var(--border-width, 1px);border-top-style:solid;border-top-style:var(--border-style, solid);border-top-color:#5c5c5c;border-top-color:var(--dark2, #5c5c5c);padding-top:1em}.klaro .cookie-modal .cm-modal .cm-body ul.cm-services li.cm-service span.cm-list-title,.klaro .cookie-modal .cm-modal .cm-body ul.cm-services li.cm-purpose span.cm-list-title,.klaro .cookie-modal .cm-modal .cm-body ul.cm-purposes li.cm-service span.cm-list-title,.klaro .cookie-modal .cm-modal .cm-body ul.cm-purposes li.cm-purpose span.cm-list-title{font-weight:600}.klaro .cookie-modal .cm-modal .cm-body ul.cm-services li.cm-service span.cm-opt-out,.klaro .cookie-modal .cm-modal .cm-body ul.cm-services li.cm-service span.cm-required,.klaro .cookie-modal .cm-modal .cm-body ul.cm-services li.cm-purpose span.cm-opt-out,.klaro .cookie-modal .cm-modal .cm-body ul.cm-services li.cm-purpose span.cm-required,.klaro .cookie-modal .cm-modal .cm-body ul.cm-purposes li.cm-service span.cm-opt-out,.klaro .cookie-modal .cm-modal .cm-body ul.cm-purposes li.cm-service span.cm-required,.klaro .cookie-modal .cm-modal .cm-body ul.cm-purposes li.cm-purpose span.cm-opt-out,.klaro .cookie-modal .cm-modal .cm-body ul.cm-purposes li.cm-purpose span.cm-required{color:#5c5c5c;color:var(--dark2, #5c5c5c);padding-left:.2em;font-size:.8em}.klaro .cookie-notice:not(.cookie-modal-notice){background-color:#333;background-color:var(--dark1, #333);z-index:999;position:fixed;width:100%;bottom:0;right:0}@media(min-width: 1024px){.klaro .cookie-notice:not(.cookie-modal-notice){border-radius:4px;border-radius:var(--border-radius, 4px);position:fixed;position:var(--notice-position, fixed);right:20px;right:var(--notice-right, 20px);left:auto;left:var(--notice-left, auto);bottom:20px;bottom:var(--notice-bottom, 20px);top:auto;top:var(--notice-top, auto);max-width:400px;max-width:var(--notice-max-width, 400px);box-shadow:0 4px 6px 0 rgba(0,0,0,.2),5px 5px 10px 0 rgba(0,0,0,.19)}}@media(max-width: 1023px){.klaro .cookie-notice:not(.cookie-modal-notice){border-style:none;border-radius:0}}.klaro .cookie-notice:not(.cookie-modal-notice).cn-embedded{position:relative;height:inherit;width:inherit;left:inherit;right:inherit;bottom:inherit;z-index:0}.klaro .cookie-notice:not(.cookie-modal-notice).cn-embedded .cn-body{padding-top:.5em}.klaro .cookie-notice:not(.cookie-modal-notice) .cn-body{margin-bottom:0;margin-right:0;bottom:0;padding:1em;padding-top:0}.klaro .cookie-notice:not(.cookie-modal-notice) .cn-body p{margin-bottom:.5em}.klaro .cookie-notice:not(.cookie-modal-notice) .cn-body p.cn-changes{text-decoration:underline}.klaro .cookie-notice:not(.cookie-modal-notice) .cn-body .cn-learn-more{display:inline-block;flex-grow:1}.klaro .cookie-notice:not(.cookie-modal-notice) .cn-body .cn-buttons{display:inline-block;margin-top:-0.5em}@media(max-width: 384px){.klaro .cookie-notice:not(.cookie-modal-notice) .cn-body .cn-buttons{width:100%}}.klaro .cookie-notice:not(.cookie-modal-notice) .cn-body .cn-buttons button.cm-btn{margin-top:.5em}@media(max-width: 384px){.klaro .cookie-notice:not(.cookie-modal-notice) .cn-body .cn-buttons button.cm-btn{width:calc(50% - 0.5em)}}.klaro .cookie-notice:not(.cookie-modal-notice) .cn-body .cn-ok{margin-top:-0.5em;display:flex;flex-flow:row;flex-wrap:wrap;justify-content:right;align-items:baseline}.klaro .cookie-notice:not(.cookie-modal-notice) .cn-body .cn-ok a,.klaro .cookie-notice:not(.cookie-modal-notice) .cn-body .cn-ok div{margin-top:.5em}.klaro .cookie-modal-notice{background-color:#333;background-color:var(--dark1, #333);color:#fafafa;color:var(--light1, #fafafa);z-index:1001;box-shadow:0 4px 6px 0 rgba(0,0,0,.2),5px 5px 10px 0 rgba(0,0,0,.19);width:100%;max-height:98%;top:50%;-ms-transform:translateY(-50%);transform:translateY(-50%);position:fixed;overflow:auto;padding:1em;padding-top:.2em}@media(min-width: 400px){.klaro .cookie-modal-notice{border-radius:4px;border-radius:var(--border-radius, 4px);position:relative;margin:0 auto;max-width:400px;height:auto;width:auto}}.klaro .cookie-modal-notice .cn-ok{display:flex;flex-flow:row;justify-content:space-between;align-items:center;margin-top:1em}.klaro .cookie-notice-hidden{display:none !important}',""]);const c=a},278:e=>{"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var r="",n=void 0!==t[5];return t[4]&&(r+="@supports (".concat(t[4],") {")),t[2]&&(r+="@media ".concat(t[2]," {")),n&&(r+="@layer".concat(t[5].length>0?" ".concat(t[5]):""," {")),r+=e(t),n&&(r+="}"),t[2]&&(r+="}"),t[4]&&(r+="}"),r})).join("")},t.i=function(e,r,n,o,i){"string"==typeof e&&(e=[[null,e,void 0]]);var a={};if(n)for(var c=0;c<this.length;c++){var s=this[c][0];null!=s&&(a[s]=!0)}for(var l=0;l<e.length;l++){var u=[].concat(e[l]);n&&a[u[0]]||(void 0!==i&&(void 0===u[5]||(u[1]="@layer".concat(u[5].length>0?" ".concat(u[5]):""," {").concat(u[1],"}")),u[5]=i),r&&(u[2]?(u[1]="@media ".concat(u[2]," {").concat(u[1],"}"),u[2]=r):u[2]=r),o&&(u[4]?(u[1]="@supports (".concat(u[4],") {").concat(u[1],"}"),u[4]=o):u[4]="".concat(o)),t.push(u))}},t}},8645:e=>{"use strict";e.exports=function(e){return e[1]}},362:(e,t,r)=>{"use strict";var n=r(6441);function o(){}function i(){}i.resetWarningCache=o,e.exports=function(){function e(e,t,r,o,i,a){if(a!==n){var c=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw c.name="Invariant Violation",c}}function t(){return e}e.isRequired=e;var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:i,resetWarningCache:o};return r.PropTypes=r,r}},2688:(e,t,r)=>{e.exports=r(362)()},6441:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},5292:e=>{"use strict";var t=[];function r(e){for(var r=-1,n=0;n<t.length;n++)if(t[n].identifier===e){r=n;break}return r}function n(e,n){for(var i={},a=[],c=0;c<e.length;c++){var s=e[c],l=n.base?s[0]+n.base:s[0],u=i[l]||0,p="".concat(l," ").concat(u);i[l]=u+1;var d=r(p),f={css:s[1],media:s[2],sourceMap:s[3],supports:s[4],layer:s[5]};if(-1!==d)t[d].references++,t[d].updater(f);else{var m=o(f,n);n.byIndex=c,t.splice(c,0,{identifier:p,updater:m,references:1})}a.push(p)}return a}function o(e,t){var r=t.domAPI(t);return r.update(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap&&t.supports===e.supports&&t.layer===e.layer)return;r.update(e=t)}else r.remove()}}e.exports=function(e,o){var i=n(e=e||[],o=o||{});return function(e){e=e||[];for(var a=0;a<i.length;a++){var c=r(i[a]);t[c].references--}for(var s=n(e,o),l=0;l<i.length;l++){var u=r(i[l]);0===t[u].references&&(t[u].updater(),t.splice(u,1))}i=s}}},9383:e=>{"use strict";var t={};e.exports=function(e,r){var n=function(e){if(void 0===t[e]){var r=document.querySelector(e);if(window.HTMLIFrameElement&&r instanceof window.HTMLIFrameElement)try{r=r.contentDocument.head}catch(e){r=null}t[e]=r}return t[e]}(e);if(!n)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");n.appendChild(r)}},9088:e=>{"use strict";e.exports=function(e){var t=document.createElement("style");return e.setAttributes(t,e.attributes),e.insert(t,e.options),t}},6884:(e,t,r)=>{"use strict";e.exports=function(e){var t=r.nc;t&&e.setAttribute("nonce",t)}},9893:e=>{"use strict";e.exports=function(e){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var t=e.insertStyleElement(e);return{update:function(r){!function(e,t,r){var n="";r.supports&&(n+="@supports (".concat(r.supports,") {")),r.media&&(n+="@media ".concat(r.media," {"));var o=void 0!==r.layer;o&&(n+="@layer".concat(r.layer.length>0?" ".concat(r.layer):""," {")),n+=r.css,o&&(n+="}"),r.media&&(n+="}"),r.supports&&(n+="}");var i=r.sourceMap;i&&"undefined"!=typeof btoa&&(n+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(i))))," */")),t.styleTagTransform(n,e,t.options)}(t,e,r)},remove:function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(t)}}}},7997:e=>{"use strict";e.exports=function(e,t){if(t.styleSheet)t.styleSheet.cssText=e;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(e))}}},8120:(e,t,r)=>{"use strict";var n=r(1483),o=r(8761),i=TypeError;e.exports=function(e){if(n(e))return e;throw new i(o(e)+" is not a function")}},2374:(e,t,r)=>{"use strict";var n=r(943),o=r(8761),i=TypeError;e.exports=function(e){if(n(e))return e;throw new i(o(e)+" is not a constructor")}},3852:(e,t,r)=>{"use strict";var n=r(735),o=String,i=TypeError;e.exports=function(e){if(n(e))return e;throw new i("Can't set "+o(e)+" as a prototype")}},7095:(e,t,r)=>{"use strict";var n=r(1),o=r(5290),i=r(5835).f,a=n("unscopables"),c=Array.prototype;void 0===c[a]&&i(c,a,{configurable:!0,value:o(null)}),e.exports=function(e){c[a][e]=!0}},4419:(e,t,r)=>{"use strict";var n=r(9105).charAt;e.exports=function(e,t,r){return t+(r?n(e,t).length:1)}},6021:(e,t,r)=>{"use strict";var n=r(4815),o=TypeError;e.exports=function(e,t){if(n(t,e))return e;throw new o("Incorrect invocation")}},2293:(e,t,r)=>{"use strict";var n=r(1704),o=String,i=TypeError;e.exports=function(e){if(n(e))return e;throw new i(o(e)+" is not an object")}},9214:(e,t,r)=>{"use strict";var n=r(8473);e.exports=n((function(){if("function"==typeof ArrayBuffer){var e=new ArrayBuffer(8);Object.isExtensible(e)&&Object.defineProperty(e,"a",{value:8})}}))},4793:(e,t,r)=>{"use strict";var n=r(2867).forEach,o=r(3152)("forEach");e.exports=o?[].forEach:function(e){return n(this,e,arguments.length>1?arguments[1]:void 0)}},6142:(e,t,r)=>{"use strict";var n=r(2914),o=r(1807),i=r(2347),a=r(8901),c=r(5299),s=r(943),l=r(6960),u=r(670),p=r(4887),d=r(6665),f=Array;e.exports=function(e){var t=i(e),r=s(this),m=arguments.length,v=m>1?arguments[1]:void 0,y=void 0!==v;y&&(v=n(v,m>2?arguments[2]:void 0));var h,b,g,k,_,w,x=d(t),S=0;if(!x||this===f&&c(x))for(h=l(t),b=r?new this(h):f(h);h>S;S++)w=y?v(t[S],S):t[S],u(b,S,w);else for(_=(k=p(t,x)).next,b=r?new this:[];!(g=o(_,k)).done;S++)w=y?a(k,v,[g.value,S],!0):g.value,u(b,S,w);return b.length=S,b}},6651:(e,t,r)=>{"use strict";var n=r(5599),o=r(3392),i=r(6960),a=function(e){return function(t,r,a){var c=n(t),s=i(c);if(0===s)return!e&&-1;var l,u=o(a,s);if(e&&r!=r){for(;s>u;)if((l=c[u++])!=l)return!0}else for(;s>u;u++)if((e||u in c)&&c[u]===r)return e||u||0;return!e&&-1}};e.exports={includes:a(!0),indexOf:a(!1)}},2867:(e,t,r)=>{"use strict";var n=r(2914),o=r(4762),i=r(2121),a=r(2347),c=r(6960),s=r(4551),l=o([].push),u=function(e){var t=1===e,r=2===e,o=3===e,u=4===e,p=6===e,d=7===e,f=5===e||p;return function(m,v,y,h){for(var b,g,k=a(m),_=i(k),w=c(_),x=n(v,y),S=0,j=h||s,O=t?j(m,w):r||d?j(m,0):void 0;w>S;S++)if((f||S in _)&&(g=x(b=_[S],S,k),e))if(t)O[S]=g;else if(g)switch(e){case 3:return!0;case 5:return b;case 6:return S;case 2:l(O,b)}else switch(e){case 4:return!1;case 7:l(O,b)}return p?-1:o||u?u:O}};e.exports={forEach:u(0),map:u(1),filter:u(2),some:u(3),every:u(4),find:u(5),findIndex:u(6),filterReject:u(7)}},4595:(e,t,r)=>{"use strict";var n=r(8473),o=r(1),i=r(6170),a=o("species");e.exports=function(e){return i>=51||!n((function(){var t=[];return(t.constructor={})[a]=function(){return{foo:1}},1!==t[e](Boolean).foo}))}},3152:(e,t,r)=>{"use strict";var n=r(8473);e.exports=function(e,t){var r=[][e];return!!r&&n((function(){r.call(null,t||function(){return 1},1)}))}},1698:(e,t,r)=>{"use strict";var n=r(4762);e.exports=n([].slice)},7354:(e,t,r)=>{"use strict";var n=r(1698),o=Math.floor,i=function(e,t){var r=e.length;if(r<8)for(var a,c,s=1;s<r;){for(c=s,a=e[s];c&&t(e[c-1],a)>0;)e[c]=e[--c];c!==s++&&(e[c]=a)}else for(var l=o(r/2),u=i(n(e,0,l),t),p=i(n(e,l),t),d=u.length,f=p.length,m=0,v=0;m<d||v<f;)e[m+v]=m<d&&v<f?t(u[m],p[v])<=0?u[m++]:p[v++]:m<d?u[m++]:p[v++];return e};e.exports=i},9703:(e,t,r)=>{"use strict";var n=r(4914),o=r(943),i=r(1704),a=r(1)("species"),c=Array;e.exports=function(e){var t;return n(e)&&(t=e.constructor,(o(t)&&(t===c||n(t.prototype))||i(t)&&null===(t=t[a]))&&(t=void 0)),void 0===t?c:t}},4551:(e,t,r)=>{"use strict";var n=r(9703);e.exports=function(e,t){return new(n(e))(0===t?0:t)}},8901:(e,t,r)=>{"use strict";var n=r(2293),o=r(6721);e.exports=function(e,t,r,i){try{return i?t(n(r)[0],r[1]):t(r)}catch(t){o(e,"throw",t)}}},1554:(e,t,r)=>{"use strict";var n=r(1)("iterator"),o=!1;try{var i=0,a={next:function(){return{done:!!i++}},return:function(){o=!0}};a[n]=function(){return this},Array.from(a,(function(){throw 2}))}catch(e){}e.exports=function(e,t){try{if(!t&&!o)return!1}catch(e){return!1}var r=!1;try{var i={};i[n]=function(){return{next:function(){return{done:r=!0}}}},e(i)}catch(e){}return r}},1278:(e,t,r)=>{"use strict";var n=r(4762),o=n({}.toString),i=n("".slice);e.exports=function(e){return i(o(e),8,-1)}},6145:(e,t,r)=>{"use strict";var n=r(4338),o=r(1483),i=r(1278),a=r(1)("toStringTag"),c=Object,s="Arguments"===i(function(){return arguments}());e.exports=n?i:function(e){var t,r,n;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(r=function(e,t){try{return e[t]}catch(e){}}(t=c(e),a))?r:s?i(t):"Object"===(n=i(t))&&o(t.callee)?"Arguments":n}},4092:(e,t,r)=>{"use strict";var n=r(5290),o=r(3864),i=r(2313),a=r(2914),c=r(6021),s=r(5983),l=r(1506),u=r(5662),p=r(5247),d=r(240),f=r(382),m=r(8041).fastKey,v=r(4483),y=v.set,h=v.getterFor;e.exports={getConstructor:function(e,t,r,u){var p=e((function(e,o){c(e,d),y(e,{type:t,index:n(null),first:void 0,last:void 0,size:0}),f||(e.size=0),s(o)||l(o,e[u],{that:e,AS_ENTRIES:r})})),d=p.prototype,v=h(t),b=function(e,t,r){var n,o,i=v(e),a=g(e,t);return a?a.value=r:(i.last=a={index:o=m(t,!0),key:t,value:r,previous:n=i.last,next:void 0,removed:!1},i.first||(i.first=a),n&&(n.next=a),f?i.size++:e.size++,"F"!==o&&(i.index[o]=a)),e},g=function(e,t){var r,n=v(e),o=m(t);if("F"!==o)return n.index[o];for(r=n.first;r;r=r.next)if(r.key===t)return r};return i(d,{clear:function(){for(var e=v(this),t=e.first;t;)t.removed=!0,t.previous&&(t.previous=t.previous.next=void 0),t=t.next;e.first=e.last=void 0,e.index=n(null),f?e.size=0:this.size=0},delete:function(e){var t=this,r=v(t),n=g(t,e);if(n){var o=n.next,i=n.previous;delete r.index[n.index],n.removed=!0,i&&(i.next=o),o&&(o.previous=i),r.first===n&&(r.first=o),r.last===n&&(r.last=i),f?r.size--:t.size--}return!!n},forEach:function(e){for(var t,r=v(this),n=a(e,arguments.length>1?arguments[1]:void 0);t=t?t.next:r.first;)for(n(t.value,t.key,this);t&&t.removed;)t=t.previous},has:function(e){return!!g(this,e)}}),i(d,r?{get:function(e){var t=g(this,e);return t&&t.value},set:function(e,t){return b(this,0===e?0:e,t)}}:{add:function(e){return b(this,e=0===e?0:e,e)}}),f&&o(d,"size",{configurable:!0,get:function(){return v(this).size}}),p},setStrong:function(e,t,r){var n=t+" Iterator",o=h(t),i=h(n);u(e,t,(function(e,t){y(this,{type:n,target:e,state:o(e),kind:t,last:void 0})}),(function(){for(var e=i(this),t=e.kind,r=e.last;r&&r.removed;)r=r.previous;return e.target&&(e.last=r=r?r.next:e.state.first)?p("keys"===t?r.key:"values"===t?r.value:[r.key,r.value],!1):(e.target=void 0,p(void 0,!0))}),r?"entries":"values",!r,!0),d(t)}}},7446:(e,t,r)=>{"use strict";var n=r(8612),o=r(8389),i=r(4762),a=r(8730),c=r(7914),s=r(8041),l=r(1506),u=r(6021),p=r(1483),d=r(5983),f=r(1704),m=r(8473),v=r(1554),y=r(2277),h=r(2429);e.exports=function(e,t,r){var b=-1!==e.indexOf("Map"),g=-1!==e.indexOf("Weak"),k=b?"set":"add",_=o[e],w=_&&_.prototype,x=_,S={},j=function(e){var t=i(w[e]);c(w,e,"add"===e?function(e){return t(this,0===e?0:e),this}:"delete"===e?function(e){return!(g&&!f(e))&&t(this,0===e?0:e)}:"get"===e?function(e){return g&&!f(e)?void 0:t(this,0===e?0:e)}:"has"===e?function(e){return!(g&&!f(e))&&t(this,0===e?0:e)}:function(e,r){return t(this,0===e?0:e,r),this})};if(a(e,!p(_)||!(g||w.forEach&&!m((function(){(new _).entries().next()})))))x=r.getConstructor(t,e,b,k),s.enable();else if(a(e,!0)){var O=new x,E=O[k](g?{}:-0,1)!==O,P=m((function(){O.has(1)})),A=v((function(e){new _(e)})),z=!g&&m((function(){for(var e=new _,t=5;t--;)e[k](t,t);return!e.has(-0)}));A||((x=t((function(e,t){u(e,w);var r=h(new _,e,x);return d(t)||l(t,r[k],{that:r,AS_ENTRIES:b}),r}))).prototype=w,w.constructor=x),(P||z)&&(j("delete"),j("has"),b&&j("get")),(z||E)&&j(k),g&&w.clear&&delete w.clear}return S[e]=x,n({global:!0,constructor:!0,forced:x!==_},S),y(x,e),g||r.setStrong(x,e,b),x}},6726:(e,t,r)=>{"use strict";var n=r(5755),o=r(9497),i=r(4961),a=r(5835);e.exports=function(e,t,r){for(var c=o(t),s=a.f,l=i.f,u=0;u<c.length;u++){var p=c[u];n(e,p)||r&&n(r,p)||s(e,p,l(t,p))}}},4522:(e,t,r)=>{"use strict";var n=r(1)("match");e.exports=function(e){var t=/./;try{"/./"[e](t)}catch(r){try{return t[n]=!1,"/./"[e](t)}catch(e){}}return!1}},9441:(e,t,r)=>{"use strict";var n=r(8473);e.exports=!n((function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype}))},5247:e=>{"use strict";e.exports=function(e,t){return{value:e,done:t}}},9037:(e,t,r)=>{"use strict";var n=r(382),o=r(5835),i=r(7738);e.exports=n?function(e,t,r){return o.f(e,t,i(1,r))}:function(e,t,r){return e[t]=r,e}},7738:e=>{"use strict";e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},670:(e,t,r)=>{"use strict";var n=r(382),o=r(5835),i=r(7738);e.exports=function(e,t,r){n?o.f(e,t,i(0,r)):e[t]=r}},6446:(e,t,r)=>{"use strict";var n=r(2293),o=r(348),i=TypeError;e.exports=function(e){if(n(this),"string"===e||"default"===e)e="string";else if("number"!==e)throw new i("Incorrect hint");return o(this,e)}},3864:(e,t,r)=>{"use strict";var n=r(169),o=r(5835);e.exports=function(e,t,r){return r.get&&n(r.get,t,{getter:!0}),r.set&&n(r.set,t,{setter:!0}),o.f(e,t,r)}},7914:(e,t,r)=>{"use strict";var n=r(1483),o=r(5835),i=r(169),a=r(2095);e.exports=function(e,t,r,c){c||(c={});var s=c.enumerable,l=void 0!==c.name?c.name:t;if(n(r)&&i(r,l,c),c.global)s?e[t]=r:a(t,r);else{try{c.unsafe?e[t]&&(s=!0):delete e[t]}catch(e){}s?e[t]=r:o.f(e,t,{value:r,enumerable:!1,configurable:!c.nonConfigurable,writable:!c.nonWritable})}return e}},2313:(e,t,r)=>{"use strict";var n=r(7914);e.exports=function(e,t,r){for(var o in t)n(e,o,t[o],r);return e}},2095:(e,t,r)=>{"use strict";var n=r(8389),o=Object.defineProperty;e.exports=function(e,t){try{o(n,e,{value:t,configurable:!0,writable:!0})}catch(r){n[e]=t}return t}},6060:(e,t,r)=>{"use strict";var n=r(8761),o=TypeError;e.exports=function(e,t){if(!delete e[t])throw new o("Cannot delete property "+n(t)+" of "+n(e))}},382:(e,t,r)=>{"use strict";var n=r(8473);e.exports=!n((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))},3145:(e,t,r)=>{"use strict";var n=r(8389),o=r(1704),i=n.document,a=o(i)&&o(i.createElement);e.exports=function(e){return a?i.createElement(e):{}}},1091:e=>{"use strict";var t=TypeError;e.exports=function(e){if(e>9007199254740991)throw t("Maximum allowed index exceeded");return e}},4842:e=>{"use strict";e.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}},1902:(e,t,r)=>{"use strict";var n=r(3145)("span").classList,o=n&&n.constructor&&n.constructor.prototype;e.exports=o===Object.prototype?void 0:o},7332:(e,t,r)=>{"use strict";var n=r(9966).match(/firefox\/(\d+)/i);e.exports=!!n&&+n[1]},6956:(e,t,r)=>{"use strict";var n=r(938),o=r(4334);e.exports=!n&&!o&&"object"==typeof window&&"object"==typeof document},5413:e=>{"use strict";e.exports="function"==typeof Bun&&Bun&&"string"==typeof Bun.version},938:e=>{"use strict";e.exports="object"==typeof Deno&&Deno&&"object"==typeof Deno.version},8996:(e,t,r)=>{"use strict";var n=r(9966);e.exports=/MSIE|Trident/.test(n)},4466:(e,t,r)=>{"use strict";var n=r(9966);e.exports=/ipad|iphone|ipod/i.test(n)&&"undefined"!=typeof Pebble},8417:(e,t,r)=>{"use strict";var n=r(9966);e.exports=/(?:ipad|iphone|ipod).*applewebkit/i.test(n)},4334:(e,t,r)=>{"use strict";var n=r(8389),o=r(1278);e.exports="process"===o(n.process)},6639:(e,t,r)=>{"use strict";var n=r(9966);e.exports=/web0s(?!.*chrome)/i.test(n)},9966:e=>{"use strict";e.exports="undefined"!=typeof navigator&&String(navigator.userAgent)||""},6170:(e,t,r)=>{"use strict";var n,o,i=r(8389),a=r(9966),c=i.process,s=i.Deno,l=c&&c.versions||s&&s.version,u=l&&l.v8;u&&(o=(n=u.split("."))[0]>0&&n[0]<4?1:+(n[0]+n[1])),!o&&a&&(!(n=a.match(/Edge\/(\d+)/))||n[1]>=74)&&(n=a.match(/Chrome\/(\d+)/))&&(o=+n[1]),e.exports=o},5158:(e,t,r)=>{"use strict";var n=r(9966).match(/AppleWebKit\/(\d+)\./);e.exports=!!n&&+n[1]},4741:e=>{"use strict";e.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},8612:(e,t,r)=>{"use strict";var n=r(8389),o=r(4961).f,i=r(9037),a=r(7914),c=r(2095),s=r(6726),l=r(8730);e.exports=function(e,t){var r,u,p,d,f,m=e.target,v=e.global,y=e.stat;if(r=v?n:y?n[m]||c(m,{}):n[m]&&n[m].prototype)for(u in t){if(d=t[u],p=e.dontCallGetSet?(f=o(r,u))&&f.value:r[u],!l(v?u:m+(y?".":"#")+u,e.forced)&&void 0!==p){if(typeof d==typeof p)continue;s(d,p)}(e.sham||p&&p.sham)&&i(d,"sham",!0),a(r,u,d,e)}}},8473:e=>{"use strict";e.exports=function(e){try{return!!e()}catch(e){return!0}}},3358:(e,t,r)=>{"use strict";r(5021);var n=r(1807),o=r(7914),i=r(8865),a=r(8473),c=r(1),s=r(9037),l=c("species"),u=RegExp.prototype;e.exports=function(e,t,r,p){var d=c(e),f=!a((function(){var t={};return t[d]=function(){return 7},7!==""[e](t)})),m=f&&!a((function(){var t=!1,r=/a/;return"split"===e&&((r={}).constructor={},r.constructor[l]=function(){return r},r.flags="",r[d]=/./[d]),r.exec=function(){return t=!0,null},r[d](""),!t}));if(!f||!m||r){var v=/./[d],y=t(d,""[e],(function(e,t,r,o,a){var c=t.exec;return c===i||c===u.exec?f&&!a?{done:!0,value:n(v,t,r,o)}:{done:!0,value:n(e,r,t,o)}:{done:!1}}));o(String.prototype,e,y[0]),o(u,d,y[1])}p&&s(u[d],"sham",!0)}},6530:(e,t,r)=>{"use strict";var n=r(8473);e.exports=!n((function(){return Object.isExtensible(Object.preventExtensions({}))}))},3067:(e,t,r)=>{"use strict";var n=r(274),o=Function.prototype,i=o.apply,a=o.call;e.exports="object"==typeof Reflect&&Reflect.apply||(n?a.bind(i):function(){return a.apply(i,arguments)})},2914:(e,t,r)=>{"use strict";var n=r(3786),o=r(8120),i=r(274),a=n(n.bind);e.exports=function(e,t){return o(e),void 0===t?e:i?a(e,t):function(){return e.apply(t,arguments)}}},274:(e,t,r)=>{"use strict";var n=r(8473);e.exports=!n((function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")}))},2164:(e,t,r)=>{"use strict";var n=r(4762),o=r(8120),i=r(1704),a=r(5755),c=r(1698),s=r(274),l=Function,u=n([].concat),p=n([].join),d={};e.exports=s?l.bind:function(e){var t=o(this),r=t.prototype,n=c(arguments,1),s=function(){var r=u(n,c(arguments));return this instanceof s?function(e,t,r){if(!a(d,t)){for(var n=[],o=0;o<t;o++)n[o]="a["+o+"]";d[t]=l("C,a","return new C("+p(n,",")+")")}return d[t](e,r)}(t,r.length,r):t.apply(e,r)};return i(r)&&(s.prototype=r),s}},1807:(e,t,r)=>{"use strict";var n=r(274),o=Function.prototype.call;e.exports=n?o.bind(o):function(){return o.apply(o,arguments)}},2048:(e,t,r)=>{"use strict";var n=r(382),o=r(5755),i=Function.prototype,a=n&&Object.getOwnPropertyDescriptor,c=o(i,"name"),s=c&&"something"===function(){}.name,l=c&&(!n||n&&a(i,"name").configurable);e.exports={EXISTS:c,PROPER:s,CONFIGURABLE:l}},680:(e,t,r)=>{"use strict";var n=r(4762),o=r(8120);e.exports=function(e,t,r){try{return n(o(Object.getOwnPropertyDescriptor(e,t)[r]))}catch(e){}}},3786:(e,t,r)=>{"use strict";var n=r(1278),o=r(4762);e.exports=function(e){if("Function"===n(e))return o(e)}},4762:(e,t,r)=>{"use strict";var n=r(274),o=Function.prototype,i=o.call,a=n&&o.bind.bind(i,i);e.exports=n?a:function(e){return function(){return i.apply(e,arguments)}}},1409:(e,t,r)=>{"use strict";var n=r(8389),o=r(1483);e.exports=function(e,t){return arguments.length<2?(r=n[e],o(r)?r:void 0):n[e]&&n[e][t];var r}},6665:(e,t,r)=>{"use strict";var n=r(6145),o=r(2564),i=r(5983),a=r(6775),c=r(1)("iterator");e.exports=function(e){if(!i(e))return o(e,c)||o(e,"@@iterator")||a[n(e)]}},4887:(e,t,r)=>{"use strict";var n=r(1807),o=r(8120),i=r(2293),a=r(8761),c=r(6665),s=TypeError;e.exports=function(e,t){var r=arguments.length<2?c(e):t;if(o(r))return i(n(r,e));throw new s(a(e)+" is not iterable")}},5215:(e,t,r)=>{"use strict";var n=r(4762),o=r(4914),i=r(1483),a=r(1278),c=r(6261),s=n([].push);e.exports=function(e){if(i(e))return e;if(o(e)){for(var t=e.length,r=[],n=0;n<t;n++){var l=e[n];"string"==typeof l?s(r,l):"number"!=typeof l&&"Number"!==a(l)&&"String"!==a(l)||s(r,c(l))}var u=r.length,p=!0;return function(e,t){if(p)return p=!1,t;if(o(this))return t;for(var n=0;n<u;n++)if(r[n]===e)return t}}}},2564:(e,t,r)=>{"use strict";var n=r(8120),o=r(5983);e.exports=function(e,t){var r=e[t];return o(r)?void 0:n(r)}},708:(e,t,r)=>{"use strict";var n=r(4762),o=r(2347),i=Math.floor,a=n("".charAt),c=n("".replace),s=n("".slice),l=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,u=/\$([$&'`]|\d{1,2})/g;e.exports=function(e,t,r,n,p,d){var f=r+e.length,m=n.length,v=u;return void 0!==p&&(p=o(p),v=l),c(d,v,(function(o,c){var l;switch(a(c,0)){case"$":return"$";case"&":return e;case"`":return s(t,0,r);case"'":return s(t,f);case"<":l=p[s(c,1,-1)];break;default:var u=+c;if(0===u)return o;if(u>m){var d=i(u/10);return 0===d?o:d<=m?void 0===n[d-1]?a(c,1):n[d-1]+a(c,1):o}l=n[u-1]}return void 0===l?"":l}))}},8389:function(e,t,r){"use strict";var n=function(e){return e&&e.Math===Math&&e};e.exports=n("object"==typeof globalThis&&globalThis)||n("object"==typeof window&&window)||n("object"==typeof self&&self)||n("object"==typeof r.g&&r.g)||n("object"==typeof this&&this)||function(){return this}()||Function("return this")()},5755:(e,t,r)=>{"use strict";var n=r(4762),o=r(2347),i=n({}.hasOwnProperty);e.exports=Object.hasOwn||function(e,t){return i(o(e),t)}},1507:e=>{"use strict";e.exports={}},1339:e=>{"use strict";e.exports=function(e,t){try{1===arguments.length?console.error(e):console.error(e,t)}catch(e){}}},2811:(e,t,r)=>{"use strict";var n=r(1409);e.exports=n("document","documentElement")},1799:(e,t,r)=>{"use strict";var n=r(382),o=r(8473),i=r(3145);e.exports=!n&&!o((function(){return 7!==Object.defineProperty(i("div"),"a",{get:function(){return 7}}).a}))},2121:(e,t,r)=>{"use strict";var n=r(4762),o=r(8473),i=r(1278),a=Object,c=n("".split);e.exports=o((function(){return!a("z").propertyIsEnumerable(0)}))?function(e){return"String"===i(e)?c(e,""):a(e)}:a},2429:(e,t,r)=>{"use strict";var n=r(1483),o=r(1704),i=r(1953);e.exports=function(e,t,r){var a,c;return i&&n(a=t.constructor)&&a!==r&&o(c=a.prototype)&&c!==r.prototype&&i(e,c),e}},7268:(e,t,r)=>{"use strict";var n=r(4762),o=r(1483),i=r(1831),a=n(Function.toString);o(i.inspectSource)||(i.inspectSource=function(e){return a(e)}),e.exports=i.inspectSource},8041:(e,t,r)=>{"use strict";var n=r(8612),o=r(4762),i=r(1507),a=r(1704),c=r(5755),s=r(5835).f,l=r(2278),u=r(2020),p=r(706),d=r(1866),f=r(6530),m=!1,v=d("meta"),y=0,h=function(e){s(e,v,{value:{objectID:"O"+y++,weakData:{}}})},b=e.exports={enable:function(){b.enable=function(){},m=!0;var e=l.f,t=o([].splice),r={};r[v]=1,e(r).length&&(l.f=function(r){for(var n=e(r),o=0,i=n.length;o<i;o++)if(n[o]===v){t(n,o,1);break}return n},n({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:u.f}))},fastKey:function(e,t){if(!a(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!c(e,v)){if(!p(e))return"F";if(!t)return"E";h(e)}return e[v].objectID},getWeakData:function(e,t){if(!c(e,v)){if(!p(e))return!0;if(!t)return!1;h(e)}return e[v].weakData},onFreeze:function(e){return f&&m&&p(e)&&!c(e,v)&&h(e),e}};i[v]=!0},4483:(e,t,r)=>{"use strict";var n,o,i,a=r(4644),c=r(8389),s=r(1704),l=r(9037),u=r(5755),p=r(1831),d=r(5409),f=r(1507),m="Object already initialized",v=c.TypeError,y=c.WeakMap;if(a||p.state){var h=p.state||(p.state=new y);h.get=h.get,h.has=h.has,h.set=h.set,n=function(e,t){if(h.has(e))throw new v(m);return t.facade=e,h.set(e,t),t},o=function(e){return h.get(e)||{}},i=function(e){return h.has(e)}}else{var b=d("state");f[b]=!0,n=function(e,t){if(u(e,b))throw new v(m);return t.facade=e,l(e,b,t),t},o=function(e){return u(e,b)?e[b]:{}},i=function(e){return u(e,b)}}e.exports={set:n,get:o,has:i,enforce:function(e){return i(e)?o(e):n(e,{})},getterFor:function(e){return function(t){var r;if(!s(t)||(r=o(t)).type!==e)throw new v("Incompatible receiver, "+e+" required");return r}}}},5299:(e,t,r)=>{"use strict";var n=r(1),o=r(6775),i=n("iterator"),a=Array.prototype;e.exports=function(e){return void 0!==e&&(o.Array===e||a[i]===e)}},4914:(e,t,r)=>{"use strict";var n=r(1278);e.exports=Array.isArray||function(e){return"Array"===n(e)}},1483:e=>{"use strict";var t="object"==typeof document&&document.all;e.exports=void 0===t&&void 0!==t?function(e){return"function"==typeof e||e===t}:function(e){return"function"==typeof e}},943:(e,t,r)=>{"use strict";var n=r(4762),o=r(8473),i=r(1483),a=r(6145),c=r(1409),s=r(7268),l=function(){},u=c("Reflect","construct"),p=/^\s*(?:class|function)\b/,d=n(p.exec),f=!p.test(l),m=function(e){if(!i(e))return!1;try{return u(l,[],e),!0}catch(e){return!1}},v=function(e){if(!i(e))return!1;switch(a(e)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return f||!!d(p,s(e))}catch(e){return!0}};v.sham=!0,e.exports=!u||o((function(){var e;return m(m.call)||!m(Object)||!m((function(){e=!0}))||e}))?v:m},8730:(e,t,r)=>{"use strict";var n=r(8473),o=r(1483),i=/#|\.prototype\./,a=function(e,t){var r=s[c(e)];return r===u||r!==l&&(o(t)?n(t):!!t)},c=a.normalize=function(e){return String(e).replace(i,".").toLowerCase()},s=a.data={},l=a.NATIVE="N",u=a.POLYFILL="P";e.exports=a},5983:e=>{"use strict";e.exports=function(e){return null==e}},1704:(e,t,r)=>{"use strict";var n=r(1483);e.exports=function(e){return"object"==typeof e?null!==e:n(e)}},735:(e,t,r)=>{"use strict";var n=r(1704);e.exports=function(e){return n(e)||null===e}},9557:e=>{"use strict";e.exports=!1},4786:(e,t,r)=>{"use strict";var n=r(1704),o=r(1278),i=r(1)("match");e.exports=function(e){var t;return n(e)&&(void 0!==(t=e[i])?!!t:"RegExp"===o(e))}},1423:(e,t,r)=>{"use strict";var n=r(1409),o=r(1483),i=r(4815),a=r(5022),c=Object;e.exports=a?function(e){return"symbol"==typeof e}:function(e){var t=n("Symbol");return o(t)&&i(t.prototype,c(e))}},1506:(e,t,r)=>{"use strict";var n=r(2914),o=r(1807),i=r(2293),a=r(8761),c=r(5299),s=r(6960),l=r(4815),u=r(4887),p=r(6665),d=r(6721),f=TypeError,m=function(e,t){this.stopped=e,this.result=t},v=m.prototype;e.exports=function(e,t,r){var y,h,b,g,k,_,w,x=r&&r.that,S=!(!r||!r.AS_ENTRIES),j=!(!r||!r.IS_RECORD),O=!(!r||!r.IS_ITERATOR),E=!(!r||!r.INTERRUPTED),P=n(t,x),A=function(e){return y&&d(y,"normal",e),new m(!0,e)},z=function(e){return S?(i(e),E?P(e[0],e[1],A):P(e[0],e[1])):E?P(e,A):P(e)};if(j)y=e.iterator;else if(O)y=e;else{if(!(h=p(e)))throw new f(a(e)+" is not iterable");if(c(h)){for(b=0,g=s(e);g>b;b++)if((k=z(e[b]))&&l(v,k))return k;return new m(!1)}y=u(e,h)}for(_=j?e.next:y.next;!(w=o(_,y)).done;){try{k=z(w.value)}catch(e){d(y,"throw",e)}if("object"==typeof k&&k&&l(v,k))return k}return new m(!1)}},6721:(e,t,r)=>{"use strict";var n=r(1807),o=r(2293),i=r(2564);e.exports=function(e,t,r){var a,c;o(e);try{if(!(a=i(e,"return"))){if("throw"===t)throw r;return r}a=n(a,e)}catch(e){c=!0,a=e}if("throw"===t)throw r;if(c)throw a;return o(a),r}},1040:(e,t,r)=>{"use strict";var n=r(1851).IteratorPrototype,o=r(5290),i=r(7738),a=r(2277),c=r(6775),s=function(){return this};e.exports=function(e,t,r,l){var u=t+" Iterator";return e.prototype=o(n,{next:i(+!l,r)}),a(e,u,!1,!0),c[u]=s,e}},5662:(e,t,r)=>{"use strict";var n=r(8612),o=r(1807),i=r(9557),a=r(2048),c=r(1483),s=r(1040),l=r(3181),u=r(1953),p=r(2277),d=r(9037),f=r(7914),m=r(1),v=r(6775),y=r(1851),h=a.PROPER,b=a.CONFIGURABLE,g=y.IteratorPrototype,k=y.BUGGY_SAFARI_ITERATORS,_=m("iterator"),w="keys",x="values",S="entries",j=function(){return this};e.exports=function(e,t,r,a,m,y,O){s(r,t,a);var E,P,A,z=function(e){if(e===m&&D)return D;if(!k&&e&&e in I)return I[e];switch(e){case w:case x:case S:return function(){return new r(this,e)}}return function(){return new r(this)}},C=t+" Iterator",T=!1,I=e.prototype,N=I[_]||I["@@iterator"]||m&&I[m],D=!k&&N||z(m),R="Array"===t&&I.entries||N;if(R&&(E=l(R.call(new e)))!==Object.prototype&&E.next&&(i||l(E)===g||(u?u(E,g):c(E[_])||f(E,_,j)),p(E,C,!0,!0),i&&(v[C]=j)),h&&m===x&&N&&N.name!==x&&(!i&&b?d(I,"name",x):(T=!0,D=function(){return o(N,this)})),m)if(P={values:z(x),keys:y?D:z(w),entries:z(S)},O)for(A in P)(k||T||!(A in I))&&f(I,A,P[A]);else n({target:t,proto:!0,forced:k||T},P);return i&&!O||I[_]===D||f(I,_,D,{name:m}),v[t]=D,P}},1851:(e,t,r)=>{"use strict";var n,o,i,a=r(8473),c=r(1483),s=r(1704),l=r(5290),u=r(3181),p=r(7914),d=r(1),f=r(9557),m=d("iterator"),v=!1;[].keys&&("next"in(i=[].keys())?(o=u(u(i)))!==Object.prototype&&(n=o):v=!0),!s(n)||a((function(){var e={};return n[m].call(e)!==e}))?n={}:f&&(n=l(n)),c(n[m])||p(n,m,(function(){return this})),e.exports={IteratorPrototype:n,BUGGY_SAFARI_ITERATORS:v}},6775:e=>{"use strict";e.exports={}},6960:(e,t,r)=>{"use strict";var n=r(8324);e.exports=function(e){return n(e.length)}},169:(e,t,r)=>{"use strict";var n=r(4762),o=r(8473),i=r(1483),a=r(5755),c=r(382),s=r(2048).CONFIGURABLE,l=r(7268),u=r(4483),p=u.enforce,d=u.get,f=String,m=Object.defineProperty,v=n("".slice),y=n("".replace),h=n([].join),b=c&&!o((function(){return 8!==m((function(){}),"length",{value:8}).length})),g=String(String).split("String"),k=e.exports=function(e,t,r){"Symbol("===v(f(t),0,7)&&(t="["+y(f(t),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),r&&r.getter&&(t="get "+t),r&&r.setter&&(t="set "+t),(!a(e,"name")||s&&e.name!==t)&&(c?m(e,"name",{value:t,configurable:!0}):e.name=t),b&&r&&a(r,"arity")&&e.length!==r.arity&&m(e,"length",{value:r.arity});try{r&&a(r,"constructor")&&r.constructor?c&&m(e,"prototype",{writable:!1}):e.prototype&&(e.prototype=void 0)}catch(e){}var n=p(e);return a(n,"source")||(n.source=h(g,"string"==typeof t?t:"")),e};Function.prototype.toString=k((function(){return i(this)&&d(this).source||l(this)}),"toString")},1703:e=>{"use strict";var t=Math.ceil,r=Math.floor;e.exports=Math.trunc||function(e){var n=+e;return(n>0?r:t)(n)}},553:(e,t,r)=>{"use strict";var n,o,i,a,c,s=r(8389),l=r(8123),u=r(2914),p=r(7007).set,d=r(5459),f=r(8417),m=r(4466),v=r(6639),y=r(4334),h=s.MutationObserver||s.WebKitMutationObserver,b=s.document,g=s.process,k=s.Promise,_=l("queueMicrotask");if(!_){var w=new d,x=function(){var e,t;for(y&&(e=g.domain)&&e.exit();t=w.get();)try{t()}catch(e){throw w.head&&n(),e}e&&e.enter()};f||y||v||!h||!b?!m&&k&&k.resolve?((a=k.resolve(void 0)).constructor=k,c=u(a.then,a),n=function(){c(x)}):y?n=function(){g.nextTick(x)}:(p=u(p,s),n=function(){p(x)}):(o=!0,i=b.createTextNode(""),new h(x).observe(i,{characterData:!0}),n=function(){i.data=o=!o}),_=function(e){w.head||n(),w.add(e)}}e.exports=_},1173:(e,t,r)=>{"use strict";var n=r(8120),o=TypeError,i=function(e){var t,r;this.promise=new e((function(e,n){if(void 0!==t||void 0!==r)throw new o("Bad Promise constructor");t=e,r=n})),this.resolve=n(t),this.reject=n(r)};e.exports.f=function(e){return new i(e)}},4989:(e,t,r)=>{"use strict";var n=r(4786),o=TypeError;e.exports=function(e){if(n(e))throw new o("The method doesn't accept regular expressions");return e}},1439:(e,t,r)=>{"use strict";var n=r(382),o=r(4762),i=r(1807),a=r(8473),c=r(3658),s=r(4347),l=r(7611),u=r(2347),p=r(2121),d=Object.assign,f=Object.defineProperty,m=o([].concat);e.exports=!d||a((function(){if(n&&1!==d({b:1},d(f({},"a",{enumerable:!0,get:function(){f(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var e={},t={},r=Symbol("assign detection"),o="abcdefghijklmnopqrst";return e[r]=7,o.split("").forEach((function(e){t[e]=e})),7!==d({},e)[r]||c(d({},t)).join("")!==o}))?function(e,t){for(var r=u(e),o=arguments.length,a=1,d=s.f,f=l.f;o>a;)for(var v,y=p(arguments[a++]),h=d?m(c(y),d(y)):c(y),b=h.length,g=0;b>g;)v=h[g++],n&&!i(f,y,v)||(r[v]=y[v]);return r}:d},5290:(e,t,r)=>{"use strict";var n,o=r(2293),i=r(5799),a=r(4741),c=r(1507),s=r(2811),l=r(3145),u=r(5409),p="prototype",d="script",f=u("IE_PROTO"),m=function(){},v=function(e){return"<"+d+">"+e+"</"+d+">"},y=function(e){e.write(v("")),e.close();var t=e.parentWindow.Object;return e=null,t},h=function(){try{n=new ActiveXObject("htmlfile")}catch(e){}var e,t,r;h="undefined"!=typeof document?document.domain&&n?y(n):(t=l("iframe"),r="java"+d+":",t.style.display="none",s.appendChild(t),t.src=String(r),(e=t.contentWindow.document).open(),e.write(v("document.F=Object")),e.close(),e.F):y(n);for(var o=a.length;o--;)delete h[p][a[o]];return h()};c[f]=!0,e.exports=Object.create||function(e,t){var r;return null!==e?(m[p]=o(e),r=new m,m[p]=null,r[f]=e):r=h(),void 0===t?r:i.f(r,t)}},5799:(e,t,r)=>{"use strict";var n=r(382),o=r(3896),i=r(5835),a=r(2293),c=r(5599),s=r(3658);t.f=n&&!o?Object.defineProperties:function(e,t){a(e);for(var r,n=c(t),o=s(t),l=o.length,u=0;l>u;)i.f(e,r=o[u++],n[r]);return e}},5835:(e,t,r)=>{"use strict";var n=r(382),o=r(1799),i=r(3896),a=r(2293),c=r(3815),s=TypeError,l=Object.defineProperty,u=Object.getOwnPropertyDescriptor,p="enumerable",d="configurable",f="writable";t.f=n?i?function(e,t,r){if(a(e),t=c(t),a(r),"function"==typeof e&&"prototype"===t&&"value"in r&&f in r&&!r[f]){var n=u(e,t);n&&n[f]&&(e[t]=r.value,r={configurable:d in r?r[d]:n[d],enumerable:p in r?r[p]:n[p],writable:!1})}return l(e,t,r)}:l:function(e,t,r){if(a(e),t=c(t),a(r),o)try{return l(e,t,r)}catch(e){}if("get"in r||"set"in r)throw new s("Accessors not supported");return"value"in r&&(e[t]=r.value),e}},4961:(e,t,r)=>{"use strict";var n=r(382),o=r(1807),i=r(7611),a=r(7738),c=r(5599),s=r(3815),l=r(5755),u=r(1799),p=Object.getOwnPropertyDescriptor;t.f=n?p:function(e,t){if(e=c(e),t=s(t),u)try{return p(e,t)}catch(e){}if(l(e,t))return a(!o(i.f,e,t),e[t])}},2020:(e,t,r)=>{"use strict";var n=r(1278),o=r(5599),i=r(2278).f,a=r(1698),c="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];e.exports.f=function(e){return c&&"Window"===n(e)?function(e){try{return i(e)}catch(e){return a(c)}}(e):i(o(e))}},2278:(e,t,r)=>{"use strict";var n=r(6742),o=r(4741).concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return n(e,o)}},4347:(e,t)=>{"use strict";t.f=Object.getOwnPropertySymbols},3181:(e,t,r)=>{"use strict";var n=r(5755),o=r(1483),i=r(2347),a=r(5409),c=r(9441),s=a("IE_PROTO"),l=Object,u=l.prototype;e.exports=c?l.getPrototypeOf:function(e){var t=i(e);if(n(t,s))return t[s];var r=t.constructor;return o(r)&&t instanceof r?r.prototype:t instanceof l?u:null}},706:(e,t,r)=>{"use strict";var n=r(8473),o=r(1704),i=r(1278),a=r(9214),c=Object.isExtensible,s=n((function(){c(1)}));e.exports=s||a?function(e){return!!o(e)&&(!a||"ArrayBuffer"!==i(e))&&(!c||c(e))}:c},4815:(e,t,r)=>{"use strict";var n=r(4762);e.exports=n({}.isPrototypeOf)},6742:(e,t,r)=>{"use strict";var n=r(4762),o=r(5755),i=r(5599),a=r(6651).indexOf,c=r(1507),s=n([].push);e.exports=function(e,t){var r,n=i(e),l=0,u=[];for(r in n)!o(c,r)&&o(n,r)&&s(u,r);for(;t.length>l;)o(n,r=t[l++])&&(~a(u,r)||s(u,r));return u}},3658:(e,t,r)=>{"use strict";var n=r(6742),o=r(4741);e.exports=Object.keys||function(e){return n(e,o)}},7611:(e,t)=>{"use strict";var r={}.propertyIsEnumerable,n=Object.getOwnPropertyDescriptor,o=n&&!r.call({1:2},1);t.f=o?function(e){var t=n(this,e);return!!t&&t.enumerable}:r},1953:(e,t,r)=>{"use strict";var n=r(680),o=r(2293),i=r(3852);e.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,r={};try{(e=n(Object.prototype,"__proto__","set"))(r,[]),t=r instanceof Array}catch(e){}return function(r,n){return o(r),i(n),t?e(r,n):r.__proto__=n,r}}():void 0)},5627:(e,t,r)=>{"use strict";var n=r(382),o=r(8473),i=r(4762),a=r(3181),c=r(3658),s=r(5599),l=i(r(7611).f),u=i([].push),p=n&&o((function(){var e=Object.create(null);return e[2]=2,!l(e,2)})),d=function(e){return function(t){for(var r,o=s(t),i=c(o),d=p&&null===a(o),f=i.length,m=0,v=[];f>m;)r=i[m++],n&&!(d?r in o:l(o,r))||u(v,e?[r,o[r]]:o[r]);return v}};e.exports={entries:d(!0),values:d(!1)}},5685:(e,t,r)=>{"use strict";var n=r(4338),o=r(6145);e.exports=n?{}.toString:function(){return"[object "+o(this)+"]"}},348:(e,t,r)=>{"use strict";var n=r(1807),o=r(1483),i=r(1704),a=TypeError;e.exports=function(e,t){var r,c;if("string"===t&&o(r=e.toString)&&!i(c=n(r,e)))return c;if(o(r=e.valueOf)&&!i(c=n(r,e)))return c;if("string"!==t&&o(r=e.toString)&&!i(c=n(r,e)))return c;throw new a("Can't convert object to primitive value")}},9497:(e,t,r)=>{"use strict";var n=r(1409),o=r(4762),i=r(2278),a=r(4347),c=r(2293),s=o([].concat);e.exports=n("Reflect","ownKeys")||function(e){var t=i.f(c(e)),r=a.f;return r?s(t,r(e)):t}},6589:(e,t,r)=>{"use strict";var n=r(8389);e.exports=n},4193:e=>{"use strict";e.exports=function(e){try{return{error:!1,value:e()}}catch(e){return{error:!0,value:e}}}},5502:(e,t,r)=>{"use strict";var n=r(8389),o=r(2832),i=r(1483),a=r(8730),c=r(7268),s=r(1),l=r(6956),u=r(938),p=r(9557),d=r(6170),f=o&&o.prototype,m=s("species"),v=!1,y=i(n.PromiseRejectionEvent),h=a("Promise",(function(){var e=c(o),t=e!==String(o);if(!t&&66===d)return!0;if(p&&(!f.catch||!f.finally))return!0;if(!d||d<51||!/native code/.test(e)){var r=new o((function(e){e(1)})),n=function(e){e((function(){}),(function(){}))};if((r.constructor={})[m]=n,!(v=r.then((function(){}))instanceof n))return!0}return!t&&(l||u)&&!y}));e.exports={CONSTRUCTOR:h,REJECTION_EVENT:y,SUBCLASSING:v}},2832:(e,t,r)=>{"use strict";var n=r(8389);e.exports=n.Promise},2172:(e,t,r)=>{"use strict";var n=r(2293),o=r(1704),i=r(1173);e.exports=function(e,t){if(n(e),o(t)&&t.constructor===e)return t;var r=i.f(e);return(0,r.resolve)(t),r.promise}},1407:(e,t,r)=>{"use strict";var n=r(2832),o=r(1554),i=r(5502).CONSTRUCTOR;e.exports=i||!o((function(e){n.all(e).then(void 0,(function(){}))}))},7150:(e,t,r)=>{"use strict";var n=r(5835).f;e.exports=function(e,t,r){r in e||n(e,r,{configurable:!0,get:function(){return t[r]},set:function(e){t[r]=e}})}},5459:e=>{"use strict";var t=function(){this.head=null,this.tail=null};t.prototype={add:function(e){var t={item:e,next:null},r=this.tail;r?r.next=t:this.head=t,this.tail=t},get:function(){var e=this.head;if(e)return null===(this.head=e.next)&&(this.tail=null),e.item}},e.exports=t},2428:(e,t,r)=>{"use strict";var n=r(1807),o=r(2293),i=r(1483),a=r(1278),c=r(8865),s=TypeError;e.exports=function(e,t){var r=e.exec;if(i(r)){var l=n(r,e,t);return null!==l&&o(l),l}if("RegExp"===a(e))return n(c,e,t);throw new s("RegExp#exec called on incompatible receiver")}},8865:(e,t,r)=>{"use strict";var n,o,i=r(1807),a=r(4762),c=r(6261),s=r(6653),l=r(7435),u=r(7255),p=r(5290),d=r(4483).get,f=r(3933),m=r(4528),v=u("native-string-replace",String.prototype.replace),y=RegExp.prototype.exec,h=y,b=a("".charAt),g=a("".indexOf),k=a("".replace),_=a("".slice),w=(o=/b*/g,i(y,n=/a/,"a"),i(y,o,"a"),0!==n.lastIndex||0!==o.lastIndex),x=l.BROKEN_CARET,S=void 0!==/()??/.exec("")[1];(w||S||x||f||m)&&(h=function(e){var t,r,n,o,a,l,u,f=this,m=d(f),j=c(e),O=m.raw;if(O)return O.lastIndex=f.lastIndex,t=i(h,O,j),f.lastIndex=O.lastIndex,t;var E=m.groups,P=x&&f.sticky,A=i(s,f),z=f.source,C=0,T=j;if(P&&(A=k(A,"y",""),-1===g(A,"g")&&(A+="g"),T=_(j,f.lastIndex),f.lastIndex>0&&(!f.multiline||f.multiline&&"\n"!==b(j,f.lastIndex-1))&&(z="(?: "+z+")",T=" "+T,C++),r=new RegExp("^(?:"+z+")",A)),S&&(r=new RegExp("^"+z+"$(?!\\s)",A)),w&&(n=f.lastIndex),o=i(y,P?r:f,T),P?o?(o.input=_(o.input,C),o[0]=_(o[0],C),o.index=f.lastIndex,f.lastIndex+=o[0].length):f.lastIndex=0:w&&o&&(f.lastIndex=f.global?o.index+o[0].length:n),S&&o&&o.length>1&&i(v,o[0],r,(function(){for(a=1;a<arguments.length-2;a++)void 0===arguments[a]&&(o[a]=void 0)})),o&&E)for(o.groups=l=p(null),a=0;a<E.length;a++)l[(u=E[a])[0]]=o[u[1]];return o}),e.exports=h},6653:(e,t,r)=>{"use strict";var n=r(2293);e.exports=function(){var e=n(this),t="";return e.hasIndices&&(t+="d"),e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.dotAll&&(t+="s"),e.unicode&&(t+="u"),e.unicodeSets&&(t+="v"),e.sticky&&(t+="y"),t}},9736:(e,t,r)=>{"use strict";var n=r(1807),o=r(5755),i=r(4815),a=r(6653),c=RegExp.prototype;e.exports=function(e){var t=e.flags;return void 0!==t||"flags"in c||o(e,"flags")||!i(c,e)?t:n(a,e)}},7435:(e,t,r)=>{"use strict";var n=r(8473),o=r(8389).RegExp,i=n((function(){var e=o("a","y");return e.lastIndex=2,null!==e.exec("abcd")})),a=i||n((function(){return!o("a","y").sticky})),c=i||n((function(){var e=o("^r","gy");return e.lastIndex=2,null!==e.exec("str")}));e.exports={BROKEN_CARET:c,MISSED_STICKY:a,UNSUPPORTED_Y:i}},3933:(e,t,r)=>{"use strict";var n=r(8473),o=r(8389).RegExp;e.exports=n((function(){var e=o(".","s");return!(e.dotAll&&e.test("\n")&&"s"===e.flags)}))},4528:(e,t,r)=>{"use strict";var n=r(8473),o=r(8389).RegExp;e.exports=n((function(){var e=o("(?<a>b)","g");return"b"!==e.exec("b").groups.a||"bc"!=="b".replace(e,"$<a>c")}))},3312:(e,t,r)=>{"use strict";var n=r(5983),o=TypeError;e.exports=function(e){if(n(e))throw new o("Can't call method on "+e);return e}},8123:(e,t,r)=>{"use strict";var n=r(8389),o=r(382),i=Object.getOwnPropertyDescriptor;e.exports=function(e){if(!o)return n[e];var t=i(n,e);return t&&t.value}},9570:(e,t,r)=>{"use strict";var n,o=r(8389),i=r(3067),a=r(1483),c=r(5413),s=r(9966),l=r(1698),u=r(4066),p=o.Function,d=/MSIE .\./.test(s)||c&&((n=o.Bun.version.split(".")).length<3||"0"===n[0]&&(n[1]<3||"3"===n[1]&&"0"===n[2]));e.exports=function(e,t){var r=t?2:1;return d?function(n,o){var c=u(arguments.length,1)>r,s=a(n)?n:p(n),d=c?l(arguments,r):[],f=c?function(){i(s,this,d)}:s;return t?e(f,o):e(f)}:e}},240:(e,t,r)=>{"use strict";var n=r(1409),o=r(3864),i=r(1),a=r(382),c=i("species");e.exports=function(e){var t=n(e);a&&t&&!t[c]&&o(t,c,{configurable:!0,get:function(){return this}})}},2277:(e,t,r)=>{"use strict";var n=r(5835).f,o=r(5755),i=r(1)("toStringTag");e.exports=function(e,t,r){e&&!r&&(e=e.prototype),e&&!o(e,i)&&n(e,i,{configurable:!0,value:t})}},5409:(e,t,r)=>{"use strict";var n=r(7255),o=r(1866),i=n("keys");e.exports=function(e){return i[e]||(i[e]=o(e))}},1831:(e,t,r)=>{"use strict";var n=r(9557),o=r(8389),i=r(2095),a="__core-js_shared__",c=e.exports=o[a]||i(a,{});(c.versions||(c.versions=[])).push({version:"3.36.0",mode:n?"pure":"global",copyright:"© 2014-2024 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.36.0/LICENSE",source:"https://github.com/zloirock/core-js"})},7255:(e,t,r)=>{"use strict";var n=r(1831);e.exports=function(e,t){return n[e]||(n[e]=t||{})}},483:(e,t,r)=>{"use strict";var n=r(2293),o=r(2374),i=r(5983),a=r(1)("species");e.exports=function(e,t){var r,c=n(e).constructor;return void 0===c||i(r=n(c)[a])?t:o(r)}},9105:(e,t,r)=>{"use strict";var n=r(4762),o=r(3005),i=r(6261),a=r(3312),c=n("".charAt),s=n("".charCodeAt),l=n("".slice),u=function(e){return function(t,r){var n,u,p=i(a(t)),d=o(r),f=p.length;return d<0||d>=f?e?"":void 0:(n=s(p,d))<55296||n>56319||d+1===f||(u=s(p,d+1))<56320||u>57343?e?c(p,d):n:e?l(p,d,d+2):u-56320+(n-55296<<10)+65536}};e.exports={codeAt:u(!1),charAt:u(!0)}},4544:(e,t,r)=>{"use strict";var n=r(4762),o=r(3312),i=r(6261),a=r(5870),c=n("".replace),s=RegExp("^["+a+"]+"),l=RegExp("(^|[^"+a+"])["+a+"]+$"),u=function(e){return function(t){var r=i(o(t));return 1&e&&(r=c(r,s,"")),2&e&&(r=c(r,l,"$1")),r}};e.exports={start:u(1),end:u(2),trim:u(3)}},6029:(e,t,r)=>{"use strict";var n=r(6170),o=r(8473),i=r(8389).String;e.exports=!!Object.getOwnPropertySymbols&&!o((function(){var e=Symbol("symbol detection");return!i(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&n&&n<41}))},8192:(e,t,r)=>{"use strict";var n=r(1807),o=r(1409),i=r(1),a=r(7914);e.exports=function(){var e=o("Symbol"),t=e&&e.prototype,r=t&&t.valueOf,c=i("toPrimitive");t&&!t[c]&&a(t,c,(function(e){return n(r,this)}),{arity:1})}},3218:(e,t,r)=>{"use strict";var n=r(6029);e.exports=n&&!!Symbol.for&&!!Symbol.keyFor},7007:(e,t,r)=>{"use strict";var n,o,i,a,c=r(8389),s=r(3067),l=r(2914),u=r(1483),p=r(5755),d=r(8473),f=r(2811),m=r(1698),v=r(3145),y=r(4066),h=r(8417),b=r(4334),g=c.setImmediate,k=c.clearImmediate,_=c.process,w=c.Dispatch,x=c.Function,S=c.MessageChannel,j=c.String,O=0,E={},P="onreadystatechange";d((function(){n=c.location}));var A=function(e){if(p(E,e)){var t=E[e];delete E[e],t()}},z=function(e){return function(){A(e)}},C=function(e){A(e.data)},T=function(e){c.postMessage(j(e),n.protocol+"//"+n.host)};g&&k||(g=function(e){y(arguments.length,1);var t=u(e)?e:x(e),r=m(arguments,1);return E[++O]=function(){s(t,void 0,r)},o(O),O},k=function(e){delete E[e]},b?o=function(e){_.nextTick(z(e))}:w&&w.now?o=function(e){w.now(z(e))}:S&&!h?(a=(i=new S).port2,i.port1.onmessage=C,o=l(a.postMessage,a)):c.addEventListener&&u(c.postMessage)&&!c.importScripts&&n&&"file:"!==n.protocol&&!d(T)?(o=T,c.addEventListener("message",C,!1)):o=P in v("script")?function(e){f.appendChild(v("script"))[P]=function(){f.removeChild(this),A(e)}}:function(e){setTimeout(z(e),0)}),e.exports={set:g,clear:k}},2430:(e,t,r)=>{"use strict";var n=r(4762);e.exports=n(1..valueOf)},3392:(e,t,r)=>{"use strict";var n=r(3005),o=Math.max,i=Math.min;e.exports=function(e,t){var r=n(e);return r<0?o(r+t,0):i(r,t)}},5599:(e,t,r)=>{"use strict";var n=r(2121),o=r(3312);e.exports=function(e){return n(o(e))}},3005:(e,t,r)=>{"use strict";var n=r(1703);e.exports=function(e){var t=+e;return t!=t||0===t?0:n(t)}},8324:(e,t,r)=>{"use strict";var n=r(3005),o=Math.min;e.exports=function(e){var t=n(e);return t>0?o(t,9007199254740991):0}},2347:(e,t,r)=>{"use strict";var n=r(3312),o=Object;e.exports=function(e){return o(n(e))}},2355:(e,t,r)=>{"use strict";var n=r(1807),o=r(1704),i=r(1423),a=r(2564),c=r(348),s=r(1),l=TypeError,u=s("toPrimitive");e.exports=function(e,t){if(!o(e)||i(e))return e;var r,s=a(e,u);if(s){if(void 0===t&&(t="default"),r=n(s,e,t),!o(r)||i(r))return r;throw new l("Can't convert object to primitive value")}return void 0===t&&(t="number"),c(e,t)}},3815:(e,t,r)=>{"use strict";var n=r(2355),o=r(1423);e.exports=function(e){var t=n(e,"string");return o(t)?t:t+""}},4338:(e,t,r)=>{"use strict";var n={};n[r(1)("toStringTag")]="z",e.exports="[object z]"===String(n)},6261:(e,t,r)=>{"use strict";var n=r(6145),o=String;e.exports=function(e){if("Symbol"===n(e))throw new TypeError("Cannot convert a Symbol value to a string");return o(e)}},8761:e=>{"use strict";var t=String;e.exports=function(e){try{return t(e)}catch(e){return"Object"}}},1866:(e,t,r)=>{"use strict";var n=r(4762),o=0,i=Math.random(),a=n(1..toString);e.exports=function(e){return"Symbol("+(void 0===e?"":e)+")_"+a(++o+i,36)}},5022:(e,t,r)=>{"use strict";var n=r(6029);e.exports=n&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},3896:(e,t,r)=>{"use strict";var n=r(382),o=r(8473);e.exports=n&&o((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},4066:e=>{"use strict";var t=TypeError;e.exports=function(e,r){if(e<r)throw new t("Not enough arguments");return e}},4644:(e,t,r)=>{"use strict";var n=r(8389),o=r(1483),i=n.WeakMap;e.exports=o(i)&&/native code/.test(String(i))},7849:(e,t,r)=>{"use strict";var n=r(6589),o=r(5755),i=r(5373),a=r(5835).f;e.exports=function(e){var t=n.Symbol||(n.Symbol={});o(t,e)||a(t,e,{value:i.f(e)})}},5373:(e,t,r)=>{"use strict";var n=r(1);t.f=n},1:(e,t,r)=>{"use strict";var n=r(8389),o=r(7255),i=r(5755),a=r(1866),c=r(6029),s=r(5022),l=n.Symbol,u=o("wks"),p=s?l.for||l:l&&l.withoutSetter||a;e.exports=function(e){return i(u,e)||(u[e]=c&&i(l,e)?l[e]:p("Symbol."+e)),u[e]}},5870:e=>{"use strict";e.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},4776:(e,t,r)=>{"use strict";var n=r(8612),o=r(8473),i=r(4914),a=r(1704),c=r(2347),s=r(6960),l=r(1091),u=r(670),p=r(4551),d=r(4595),f=r(1),m=r(6170),v=f("isConcatSpreadable"),y=m>=51||!o((function(){var e=[];return e[v]=!1,e.concat()[0]!==e})),h=function(e){if(!a(e))return!1;var t=e[v];return void 0!==t?!!t:i(e)};n({target:"Array",proto:!0,arity:1,forced:!y||!d("concat")},{concat:function(e){var t,r,n,o,i,a=c(this),d=p(a,0),f=0;for(t=-1,n=arguments.length;t<n;t++)if(h(i=-1===t?a:arguments[t]))for(o=s(i),l(f+o),r=0;r<o;r++,f++)r in i&&u(d,f,i[r]);else l(f+1),u(d,f++,i);return d.length=f,d}})},4382:(e,t,r)=>{"use strict";var n=r(8612),o=r(2867).filter;n({target:"Array",proto:!0,forced:!r(4595)("filter")},{filter:function(e){return o(this,e,arguments.length>1?arguments[1]:void 0)}})},2084:(e,t,r)=>{"use strict";var n=r(8612),o=r(2867).find,i=r(7095),a="find",c=!0;a in[]&&Array(1)[a]((function(){c=!1})),n({target:"Array",proto:!0,forced:c},{find:function(e){return o(this,e,arguments.length>1?arguments[1]:void 0)}}),i(a)},9892:(e,t,r)=>{"use strict";var n=r(8612),o=r(6142);n({target:"Array",stat:!0,forced:!r(1554)((function(e){Array.from(e)}))},{from:o})},6281:(e,t,r)=>{"use strict";var n=r(8612),o=r(6651).includes,i=r(8473),a=r(7095);n({target:"Array",proto:!0,forced:i((function(){return!Array(1).includes()}))},{includes:function(e){return o(this,e,arguments.length>1?arguments[1]:void 0)}}),a("includes")},4962:(e,t,r)=>{"use strict";var n=r(5599),o=r(7095),i=r(6775),a=r(4483),c=r(5835).f,s=r(5662),l=r(5247),u=r(9557),p=r(382),d="Array Iterator",f=a.set,m=a.getterFor(d);e.exports=s(Array,"Array",(function(e,t){f(this,{type:d,target:n(e),index:0,kind:t})}),(function(){var e=m(this),t=e.target,r=e.index++;if(!t||r>=t.length)return e.target=void 0,l(void 0,!0);switch(e.kind){case"keys":return l(r,!1);case"values":return l(t[r],!1)}return l([r,t[r]],!1)}),"values");var v=i.Arguments=i.Array;if(o("keys"),o("values"),o("entries"),!u&&p&&"values"!==v.name)try{c(v,"name",{value:"values"})}catch(e){}},6216:(e,t,r)=>{"use strict";var n=r(8612),o=r(4762),i=r(2121),a=r(5599),c=r(3152),s=o([].join);n({target:"Array",proto:!0,forced:i!==Object||!c("join",",")},{join:function(e){return s(a(this),void 0===e?",":e)}})},6584:(e,t,r)=>{"use strict";var n=r(8612),o=r(2867).map;n({target:"Array",proto:!0,forced:!r(4595)("map")},{map:function(e){return o(this,e,arguments.length>1?arguments[1]:void 0)}})},9336:(e,t,r)=>{"use strict";var n=r(8612),o=r(4914),i=r(943),a=r(1704),c=r(3392),s=r(6960),l=r(5599),u=r(670),p=r(1),d=r(4595),f=r(1698),m=d("slice"),v=p("species"),y=Array,h=Math.max;n({target:"Array",proto:!0,forced:!m},{slice:function(e,t){var r,n,p,d=l(this),m=s(d),b=c(e,m),g=c(void 0===t?m:t,m);if(o(d)&&(r=d.constructor,(i(r)&&(r===y||o(r.prototype))||a(r)&&null===(r=r[v]))&&(r=void 0),r===y||void 0===r))return f(d,b,g);for(n=new(void 0===r?y:r)(h(g-b,0)),p=0;b<g;b++,p++)b in d&&u(n,p,d[b]);return n.length=p,n}})},6448:(e,t,r)=>{"use strict";var n=r(8612),o=r(4762),i=r(8120),a=r(2347),c=r(6960),s=r(6060),l=r(6261),u=r(8473),p=r(7354),d=r(3152),f=r(7332),m=r(8996),v=r(6170),y=r(5158),h=[],b=o(h.sort),g=o(h.push),k=u((function(){h.sort(void 0)})),_=u((function(){h.sort(null)})),w=d("sort"),x=!u((function(){if(v)return v<70;if(!(f&&f>3)){if(m)return!0;if(y)return y<603;var e,t,r,n,o="";for(e=65;e<76;e++){switch(t=String.fromCharCode(e),e){case 66:case 69:case 70:case 72:r=3;break;case 68:case 71:r=4;break;default:r=2}for(n=0;n<47;n++)h.push({k:t+n,v:r})}for(h.sort((function(e,t){return t.v-e.v})),n=0;n<h.length;n++)t=h[n].k.charAt(0),o.charAt(o.length-1)!==t&&(o+=t);return"DGBEFHACIJK"!==o}}));n({target:"Array",proto:!0,forced:k||!_||!w||!x},{sort:function(e){void 0!==e&&i(e);var t=a(this);if(x)return void 0===e?b(t):b(t,e);var r,n,o=[],u=c(t);for(n=0;n<u;n++)n in t&&g(o,t[n]);for(p(o,function(e){return function(t,r){return void 0===r?-1:void 0===t?1:void 0!==e?+e(t,r)||0:l(t)>l(r)?1:-1}}(e)),r=c(o),n=0;n<r;)t[n]=o[n++];for(;n<u;)s(t,n++);return t}})},4754:(e,t,r)=>{"use strict";var n=r(5755),o=r(7914),i=r(6446),a=r(1)("toPrimitive"),c=Date.prototype;n(c,a)||o(c,a,i)},1908:(e,t,r)=>{"use strict";var n=r(382),o=r(2048).EXISTS,i=r(4762),a=r(3864),c=Function.prototype,s=i(c.toString),l=/function\b(?:\s|\/\*[\S\s]*?\*\/|\/\/[^\n\r]*[\n\r]+)*([^\s(/]*)/,u=i(l.exec);n&&!o&&a(c,"name",{configurable:!0,get:function(){try{return u(l,s(this))[1]}catch(e){return""}}})},6184:(e,t,r)=>{"use strict";var n=r(8612),o=r(1409),i=r(3067),a=r(1807),c=r(4762),s=r(8473),l=r(1483),u=r(1423),p=r(1698),d=r(5215),f=r(6029),m=String,v=o("JSON","stringify"),y=c(/./.exec),h=c("".charAt),b=c("".charCodeAt),g=c("".replace),k=c(1..toString),_=/[\uD800-\uDFFF]/g,w=/^[\uD800-\uDBFF]$/,x=/^[\uDC00-\uDFFF]$/,S=!f||s((function(){var e=o("Symbol")("stringify detection");return"[null]"!==v([e])||"{}"!==v({a:e})||"{}"!==v(Object(e))})),j=s((function(){return'"\\udf06\\ud834"'!==v("\udf06\ud834")||'"\\udead"'!==v("\udead")})),O=function(e,t){var r=p(arguments),n=d(t);if(l(n)||void 0!==e&&!u(e))return r[1]=function(e,t){if(l(n)&&(t=a(n,this,m(e),t)),!u(t))return t},i(v,null,r)},E=function(e,t,r){var n=h(r,t-1),o=h(r,t+1);return y(w,e)&&!y(x,o)||y(x,e)&&!y(w,n)?"\\u"+k(b(e,0),16):e};v&&n({target:"JSON",stat:!0,arity:3,forced:S||j},{stringify:function(e,t,r){var n=p(arguments),o=i(S?O:v,null,n);return j&&"string"==typeof o?g(o,_,E):o}})},2725:(e,t,r)=>{"use strict";r(7446)("Map",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),r(4092))},8551:(e,t,r)=>{"use strict";r(2725)},94:(e,t,r)=>{"use strict";var n=r(8612),o=r(9557),i=r(382),a=r(8389),c=r(6589),s=r(4762),l=r(8730),u=r(5755),p=r(2429),d=r(4815),f=r(1423),m=r(2355),v=r(8473),y=r(2278).f,h=r(4961).f,b=r(5835).f,g=r(2430),k=r(4544).trim,_="Number",w=a[_],x=c[_],S=w.prototype,j=a.TypeError,O=s("".slice),E=s("".charCodeAt),P=l(_,!w(" 0o1")||!w("0b1")||w("+0x1")),A=function(e){var t,r=arguments.length<1?0:w(function(e){var t=m(e,"number");return"bigint"==typeof t?t:function(e){var t,r,n,o,i,a,c,s,l=m(e,"number");if(f(l))throw new j("Cannot convert a Symbol value to a number");if("string"==typeof l&&l.length>2)if(l=k(l),43===(t=E(l,0))||45===t){if(88===(r=E(l,2))||120===r)return NaN}else if(48===t){switch(E(l,1)){case 66:case 98:n=2,o=49;break;case 79:case 111:n=8,o=55;break;default:return+l}for(a=(i=O(l,2)).length,c=0;c<a;c++)if((s=E(i,c))<48||s>o)return NaN;return parseInt(i,n)}return+l}(t)}(e));return d(S,t=this)&&v((function(){g(t)}))?p(Object(r),this,A):r};A.prototype=S,P&&!o&&(S.constructor=A),n({global:!0,constructor:!0,wrap:!0,forced:P},{Number:A});var z=function(e,t){for(var r,n=i?y(t):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),o=0;n.length>o;o++)u(t,r=n[o])&&!u(e,r)&&b(e,r,h(t,r))};o&&x&&z(c[_],x),(P||o)&&z(c[_],w)},7575:(e,t,r)=>{"use strict";var n=r(8612),o=r(1439);n({target:"Object",stat:!0,arity:2,forced:Object.assign!==o},{assign:o})},7132:(e,t,r)=>{"use strict";var n=r(8612),o=r(5627).entries;n({target:"Object",stat:!0},{entries:function(e){return o(e)}})},6457:(e,t,r)=>{"use strict";var n=r(8612),o=r(8473),i=r(5599),a=r(4961).f,c=r(382);n({target:"Object",stat:!0,forced:!c||o((function(){a(1)})),sham:!c},{getOwnPropertyDescriptor:function(e,t){return a(i(e),t)}})},8908:(e,t,r)=>{"use strict";var n=r(8612),o=r(382),i=r(9497),a=r(5599),c=r(4961),s=r(670);n({target:"Object",stat:!0,sham:!o},{getOwnPropertyDescriptors:function(e){for(var t,r,n=a(e),o=c.f,l=i(n),u={},p=0;l.length>p;)void 0!==(r=o(n,t=l[p++]))&&s(u,t,r);return u}})},7859:(e,t,r)=>{"use strict";var n=r(8612),o=r(6029),i=r(8473),a=r(4347),c=r(2347);n({target:"Object",stat:!0,forced:!o||i((function(){a.f(1)}))},{getOwnPropertySymbols:function(e){var t=a.f;return t?t(c(e)):[]}})},6437:(e,t,r)=>{"use strict";var n=r(8612),o=r(8473),i=r(2347),a=r(3181),c=r(9441);n({target:"Object",stat:!0,forced:o((function(){a(1)})),sham:!c},{getPrototypeOf:function(e){return a(i(e))}})},3810:(e,t,r)=>{"use strict";var n=r(8612),o=r(2347),i=r(3658);n({target:"Object",stat:!0,forced:r(8473)((function(){i(1)}))},{keys:function(e){return i(o(e))}})},2697:(e,t,r)=>{"use strict";r(8612)({target:"Object",stat:!0},{setPrototypeOf:r(1953)})},8557:(e,t,r)=>{"use strict";var n=r(4338),o=r(7914),i=r(5685);n||o(Object.prototype,"toString",i,{unsafe:!0})},6249:(e,t,r)=>{"use strict";var n=r(8612),o=r(1807),i=r(8120),a=r(1173),c=r(4193),s=r(1506);n({target:"Promise",stat:!0,forced:r(1407)},{all:function(e){var t=this,r=a.f(t),n=r.resolve,l=r.reject,u=c((function(){var r=i(t.resolve),a=[],c=0,u=1;s(e,(function(e){var i=c++,s=!1;u++,o(r,t,e).then((function(e){s||(s=!0,a[i]=e,--u||n(a))}),l)})),--u||n(a)}));return u.error&&l(u.value),r.promise}})},6681:(e,t,r)=>{"use strict";var n=r(8612),o=r(9557),i=r(5502).CONSTRUCTOR,a=r(2832),c=r(1409),s=r(1483),l=r(7914),u=a&&a.prototype;if(n({target:"Promise",proto:!0,forced:i,real:!0},{catch:function(e){return this.then(void 0,e)}}),!o&&s(a)){var p=c("Promise").prototype.catch;u.catch!==p&&l(u,"catch",p,{unsafe:!0})}},8786:(e,t,r)=>{"use strict";var n,o,i,a=r(8612),c=r(9557),s=r(4334),l=r(8389),u=r(1807),p=r(7914),d=r(1953),f=r(2277),m=r(240),v=r(8120),y=r(1483),h=r(1704),b=r(6021),g=r(483),k=r(7007).set,_=r(553),w=r(1339),x=r(4193),S=r(5459),j=r(4483),O=r(2832),E=r(5502),P=r(1173),A="Promise",z=E.CONSTRUCTOR,C=E.REJECTION_EVENT,T=E.SUBCLASSING,I=j.getterFor(A),N=j.set,D=O&&O.prototype,R=O,M=D,q=l.TypeError,U=l.document,L=l.process,F=P.f,B=F,H=!!(U&&U.createEvent&&l.dispatchEvent),V="unhandledrejection",W=function(e){var t;return!(!h(e)||!y(t=e.then))&&t},K=function(e,t){var r,n,o,i=t.value,a=1===t.state,c=a?e.ok:e.fail,s=e.resolve,l=e.reject,p=e.domain;try{c?(a||(2===t.rejection&&Y(t),t.rejection=1),!0===c?r=i:(p&&p.enter(),r=c(i),p&&(p.exit(),o=!0)),r===e.promise?l(new q("Promise-chain cycle")):(n=W(r))?u(n,r,s,l):s(r)):l(i)}catch(e){p&&!o&&p.exit(),l(e)}},$=function(e,t){e.notified||(e.notified=!0,_((function(){for(var r,n=e.reactions;r=n.get();)K(r,e);e.notified=!1,t&&!e.rejection&&Z(e)})))},G=function(e,t,r){var n,o;H?((n=U.createEvent("Event")).promise=t,n.reason=r,n.initEvent(e,!1,!0),l.dispatchEvent(n)):n={promise:t,reason:r},!C&&(o=l["on"+e])?o(n):e===V&&w("Unhandled promise rejection",r)},Z=function(e){u(k,l,(function(){var t,r=e.facade,n=e.value;if(X(e)&&(t=x((function(){s?L.emit("unhandledRejection",n,r):G(V,r,n)})),e.rejection=s||X(e)?2:1,t.error))throw t.value}))},X=function(e){return 1!==e.rejection&&!e.parent},Y=function(e){u(k,l,(function(){var t=e.facade;s?L.emit("rejectionHandled",t):G("rejectionhandled",t,e.value)}))},J=function(e,t,r){return function(n){e(t,n,r)}},Q=function(e,t,r){e.done||(e.done=!0,r&&(e=r),e.value=t,e.state=2,$(e,!0))},ee=function(e,t,r){if(!e.done){e.done=!0,r&&(e=r);try{if(e.facade===t)throw new q("Promise can't be resolved itself");var n=W(t);n?_((function(){var r={done:!1};try{u(n,t,J(ee,r,e),J(Q,r,e))}catch(t){Q(r,t,e)}})):(e.value=t,e.state=1,$(e,!1))}catch(t){Q({done:!1},t,e)}}};if(z&&(M=(R=function(e){b(this,M),v(e),u(n,this);var t=I(this);try{e(J(ee,t),J(Q,t))}catch(e){Q(t,e)}}).prototype,(n=function(e){N(this,{type:A,done:!1,notified:!1,parent:!1,reactions:new S,rejection:!1,state:0,value:void 0})}).prototype=p(M,"then",(function(e,t){var r=I(this),n=F(g(this,R));return r.parent=!0,n.ok=!y(e)||e,n.fail=y(t)&&t,n.domain=s?L.domain:void 0,0===r.state?r.reactions.add(n):_((function(){K(n,r)})),n.promise})),o=function(){var e=new n,t=I(e);this.promise=e,this.resolve=J(ee,t),this.reject=J(Q,t)},P.f=F=function(e){return e===R||void 0===e?new o(e):B(e)},!c&&y(O)&&D!==Object.prototype)){i=D.then,T||p(D,"then",(function(e,t){var r=this;return new R((function(e,t){u(i,r,e,t)})).then(e,t)}),{unsafe:!0});try{delete D.constructor}catch(e){}d&&d(D,M)}a({global:!0,constructor:!0,wrap:!0,forced:z},{Promise:R}),f(R,A,!1,!0),m(A)},76:(e,t,r)=>{"use strict";r(8786),r(6249),r(6681),r(1681),r(9231),r(5774)},1681:(e,t,r)=>{"use strict";var n=r(8612),o=r(1807),i=r(8120),a=r(1173),c=r(4193),s=r(1506);n({target:"Promise",stat:!0,forced:r(1407)},{race:function(e){var t=this,r=a.f(t),n=r.reject,l=c((function(){var a=i(t.resolve);s(e,(function(e){o(a,t,e).then(r.resolve,n)}))}));return l.error&&n(l.value),r.promise}})},9231:(e,t,r)=>{"use strict";var n=r(8612),o=r(1173);n({target:"Promise",stat:!0,forced:r(5502).CONSTRUCTOR},{reject:function(e){var t=o.f(this);return(0,t.reject)(e),t.promise}})},5774:(e,t,r)=>{"use strict";var n=r(8612),o=r(1409),i=r(9557),a=r(2832),c=r(5502).CONSTRUCTOR,s=r(2172),l=o("Promise"),u=i&&!c;n({target:"Promise",stat:!0,forced:i||c},{resolve:function(e){return s(u&&this===l?a:this,e)}})},1359:(e,t,r)=>{"use strict";var n=r(8612),o=r(1409),i=r(3067),a=r(2164),c=r(2374),s=r(2293),l=r(1704),u=r(5290),p=r(8473),d=o("Reflect","construct"),f=Object.prototype,m=[].push,v=p((function(){function e(){}return!(d((function(){}),[],e)instanceof e)})),y=!p((function(){d((function(){}))})),h=v||y;n({target:"Reflect",stat:!0,forced:h,sham:h},{construct:function(e,t){c(e),s(t);var r=arguments.length<3?e:c(arguments[2]);if(y&&!v)return d(e,t,r);if(e===r){switch(t.length){case 0:return new e;case 1:return new e(t[0]);case 2:return new e(t[0],t[1]);case 3:return new e(t[0],t[1],t[2]);case 4:return new e(t[0],t[1],t[2],t[3])}var n=[null];return i(m,n,t),new(i(a,e,n))}var o=r.prototype,p=u(l(o)?o:f),h=i(e,p,t);return l(h)?h:p}})},646:(e,t,r)=>{"use strict";var n=r(382),o=r(8389),i=r(4762),a=r(8730),c=r(2429),s=r(9037),l=r(5290),u=r(2278).f,p=r(4815),d=r(4786),f=r(6261),m=r(9736),v=r(7435),y=r(7150),h=r(7914),b=r(8473),g=r(5755),k=r(4483).enforce,_=r(240),w=r(1),x=r(3933),S=r(4528),j=w("match"),O=o.RegExp,E=O.prototype,P=o.SyntaxError,A=i(E.exec),z=i("".charAt),C=i("".replace),T=i("".indexOf),I=i("".slice),N=/^\?<[^\s\d!#%&*+<=>@^][^\s!#%&*+<=>@^]*>/,D=/a/g,R=/a/g,M=new O(D)!==D,q=v.MISSED_STICKY,U=v.UNSUPPORTED_Y;if(a("RegExp",n&&(!M||q||x||S||b((function(){return R[j]=!1,O(D)!==D||O(R)===R||"/a/i"!==String(O(D,"i"))}))))){for(var L=function(e,t){var r,n,o,i,a,u,v=p(E,this),y=d(e),h=void 0===t,b=[],_=e;if(!v&&y&&h&&e.constructor===L)return e;if((y||p(E,e))&&(e=e.source,h&&(t=m(_))),e=void 0===e?"":f(e),t=void 0===t?"":f(t),_=e,x&&"dotAll"in D&&(n=!!t&&T(t,"s")>-1)&&(t=C(t,/s/g,"")),r=t,q&&"sticky"in D&&(o=!!t&&T(t,"y")>-1)&&U&&(t=C(t,/y/g,"")),S&&(i=function(e){for(var t,r=e.length,n=0,o="",i=[],a=l(null),c=!1,s=!1,u=0,p="";n<=r;n++){if("\\"===(t=z(e,n)))t+=z(e,++n);else if("]"===t)c=!1;else if(!c)switch(!0){case"["===t:c=!0;break;case"("===t:A(N,I(e,n+1))&&(n+=2,s=!0),o+=t,u++;continue;case">"===t&&s:if(""===p||g(a,p))throw new P("Invalid capture group name");a[p]=!0,i[i.length]=[p,u],s=!1,p="";continue}s?p+=t:o+=t}return[o,i]}(e),e=i[0],b=i[1]),a=c(O(e,t),v?this:E,L),(n||o||b.length)&&(u=k(a),n&&(u.dotAll=!0,u.raw=L(function(e){for(var t,r=e.length,n=0,o="",i=!1;n<=r;n++)"\\"!==(t=z(e,n))?i||"."!==t?("["===t?i=!0:"]"===t&&(i=!1),o+=t):o+="[\\s\\S]":o+=t+z(e,++n);return o}(e),r)),o&&(u.sticky=!0),b.length&&(u.groups=b)),e!==_)try{s(a,"source",""===_?"(?:)":_)}catch(e){}return a},F=u(O),B=0;F.length>B;)y(L,O,F[B++]);E.constructor=L,L.prototype=E,h(o,"RegExp",L,{constructor:!0})}_("RegExp")},5021:(e,t,r)=>{"use strict";var n=r(8612),o=r(8865);n({target:"RegExp",proto:!0,forced:/./.exec!==o},{exec:o})},3687:(e,t,r)=>{"use strict";var n=r(2048).PROPER,o=r(7914),i=r(2293),a=r(6261),c=r(8473),s=r(9736),l="toString",u=RegExp.prototype,p=u[l],d=c((function(){return"/a/b"!==p.call({source:"a",flags:"b"})})),f=n&&p.name!==l;(d||f)&&o(u,l,(function(){var e=i(this);return"/"+a(e.source)+"/"+a(s(e))}),{unsafe:!0})},9203:(e,t,r)=>{"use strict";r(7446)("Set",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),r(4092))},2745:(e,t,r)=>{"use strict";r(9203)},987:(e,t,r)=>{"use strict";var n,o=r(8612),i=r(3786),a=r(4961).f,c=r(8324),s=r(6261),l=r(4989),u=r(3312),p=r(4522),d=r(9557),f=i("".slice),m=Math.min,v=p("endsWith");o({target:"String",proto:!0,forced:!(!d&&!v&&(n=a(String.prototype,"endsWith"),n&&!n.writable)||v)},{endsWith:function(e){var t=s(u(this));l(e);var r=arguments.length>1?arguments[1]:void 0,n=t.length,o=void 0===r?n:m(c(r),n),i=s(e);return f(t,o-i.length,o)===i}})},9425:(e,t,r)=>{"use strict";var n=r(8612),o=r(4762),i=r(4989),a=r(3312),c=r(6261),s=r(4522),l=o("".indexOf);n({target:"String",proto:!0,forced:!s("includes")},{includes:function(e){return!!~l(c(a(this)),c(i(e)),arguments.length>1?arguments[1]:void 0)}})},3994:(e,t,r)=>{"use strict";var n=r(9105).charAt,o=r(6261),i=r(4483),a=r(5662),c=r(5247),s="String Iterator",l=i.set,u=i.getterFor(s);a(String,"String",(function(e){l(this,{type:s,string:o(e),index:0})}),(function(){var e,t=u(this),r=t.string,o=t.index;return o>=r.length?c(void 0,!0):(e=n(r,o),t.index+=e.length,c(e,!1))}))},3819:(e,t,r)=>{"use strict";var n=r(1807),o=r(3358),i=r(2293),a=r(5983),c=r(8324),s=r(6261),l=r(3312),u=r(2564),p=r(4419),d=r(2428);o("match",(function(e,t,r){return[function(t){var r=l(this),o=a(t)?void 0:u(t,e);return o?n(o,t,r):new RegExp(t)[e](s(r))},function(e){var n=i(this),o=s(e),a=r(t,n,o);if(a.done)return a.value;if(!n.global)return d(n,o);var l=n.unicode;n.lastIndex=0;for(var u,f=[],m=0;null!==(u=d(n,o));){var v=s(u[0]);f[m]=v,""===v&&(n.lastIndex=p(o,c(n.lastIndex),l)),m++}return 0===m?null:f}]}))},3062:(e,t,r)=>{"use strict";var n=r(3067),o=r(1807),i=r(4762),a=r(3358),c=r(8473),s=r(2293),l=r(1483),u=r(5983),p=r(3005),d=r(8324),f=r(6261),m=r(3312),v=r(4419),y=r(2564),h=r(708),b=r(2428),g=r(1)("replace"),k=Math.max,_=Math.min,w=i([].concat),x=i([].push),S=i("".indexOf),j=i("".slice),O="$0"==="a".replace(/./,"$0"),E=!!/./[g]&&""===/./[g]("a","$0");a("replace",(function(e,t,r){var i=E?"$":"$0";return[function(e,r){var n=m(this),i=u(e)?void 0:y(e,g);return i?o(i,e,n,r):o(t,f(n),e,r)},function(e,o){var a=s(this),c=f(e);if("string"==typeof o&&-1===S(o,i)&&-1===S(o,"$<")){var u=r(t,a,c,o);if(u.done)return u.value}var m=l(o);m||(o=f(o));var y,g=a.global;g&&(y=a.unicode,a.lastIndex=0);for(var O,E=[];null!==(O=b(a,c))&&(x(E,O),g);)""===f(O[0])&&(a.lastIndex=v(c,d(a.lastIndex),y));for(var P,A="",z=0,C=0;C<E.length;C++){for(var T,I=f((O=E[C])[0]),N=k(_(p(O.index),c.length),0),D=[],R=1;R<O.length;R++)x(D,void 0===(P=O[R])?P:String(P));var M=O.groups;if(m){var q=w([I],D,N,c);void 0!==M&&x(q,M),T=f(n(o,void 0,q))}else T=h(I,c,N,D,M,o);N>=z&&(A+=j(c,z,N)+T,z=N+I.length)}return A+j(c,z)}]}),!!c((function(){var e=/./;return e.exec=function(){var e=[];return e.groups={a:"7"},e},"7"!=="".replace(e,"$<a>")}))||!O||E)},4062:(e,t,r)=>{"use strict";var n,o=r(8612),i=r(3786),a=r(4961).f,c=r(8324),s=r(6261),l=r(4989),u=r(3312),p=r(4522),d=r(9557),f=i("".slice),m=Math.min,v=p("startsWith");o({target:"String",proto:!0,forced:!(!d&&!v&&(n=a(String.prototype,"startsWith"),n&&!n.writable)||v)},{startsWith:function(e){var t=s(u(this));l(e);var r=c(m(arguments.length>1?arguments[1]:void 0,t.length)),n=s(e);return f(t,r,r+n.length)===n}})},5443:(e,t,r)=>{"use strict";var n=r(8612),o=r(8389),i=r(1807),a=r(4762),c=r(9557),s=r(382),l=r(6029),u=r(8473),p=r(5755),d=r(4815),f=r(2293),m=r(5599),v=r(3815),y=r(6261),h=r(7738),b=r(5290),g=r(3658),k=r(2278),_=r(2020),w=r(4347),x=r(4961),S=r(5835),j=r(5799),O=r(7611),E=r(7914),P=r(3864),A=r(7255),z=r(5409),C=r(1507),T=r(1866),I=r(1),N=r(5373),D=r(7849),R=r(8192),M=r(2277),q=r(4483),U=r(2867).forEach,L=z("hidden"),F="Symbol",B="prototype",H=q.set,V=q.getterFor(F),W=Object[B],K=o.Symbol,$=K&&K[B],G=o.RangeError,Z=o.TypeError,X=o.QObject,Y=x.f,J=S.f,Q=_.f,ee=O.f,te=a([].push),re=A("symbols"),ne=A("op-symbols"),oe=A("wks"),ie=!X||!X[B]||!X[B].findChild,ae=function(e,t,r){var n=Y(W,t);n&&delete W[t],J(e,t,r),n&&e!==W&&J(W,t,n)},ce=s&&u((function(){return 7!==b(J({},"a",{get:function(){return J(this,"a",{value:7}).a}})).a}))?ae:J,se=function(e,t){var r=re[e]=b($);return H(r,{type:F,tag:e,description:t}),s||(r.description=t),r},le=function(e,t,r){e===W&&le(ne,t,r),f(e);var n=v(t);return f(r),p(re,n)?(r.enumerable?(p(e,L)&&e[L][n]&&(e[L][n]=!1),r=b(r,{enumerable:h(0,!1)})):(p(e,L)||J(e,L,h(1,b(null))),e[L][n]=!0),ce(e,n,r)):J(e,n,r)},ue=function(e,t){f(e);var r=m(t),n=g(r).concat(me(r));return U(n,(function(t){s&&!i(pe,r,t)||le(e,t,r[t])})),e},pe=function(e){var t=v(e),r=i(ee,this,t);return!(this===W&&p(re,t)&&!p(ne,t))&&(!(r||!p(this,t)||!p(re,t)||p(this,L)&&this[L][t])||r)},de=function(e,t){var r=m(e),n=v(t);if(r!==W||!p(re,n)||p(ne,n)){var o=Y(r,n);return!o||!p(re,n)||p(r,L)&&r[L][n]||(o.enumerable=!0),o}},fe=function(e){var t=Q(m(e)),r=[];return U(t,(function(e){p(re,e)||p(C,e)||te(r,e)})),r},me=function(e){var t=e===W,r=Q(t?ne:m(e)),n=[];return U(r,(function(e){!p(re,e)||t&&!p(W,e)||te(n,re[e])})),n};l||(E($=(K=function(){if(d($,this))throw new Z("Symbol is not a constructor");var e=arguments.length&&void 0!==arguments[0]?y(arguments[0]):void 0,t=T(e),r=function(e){var n=void 0===this?o:this;n===W&&i(r,ne,e),p(n,L)&&p(n[L],t)&&(n[L][t]=!1);var a=h(1,e);try{ce(n,t,a)}catch(e){if(!(e instanceof G))throw e;ae(n,t,a)}};return s&&ie&&ce(W,t,{configurable:!0,set:r}),se(t,e)})[B],"toString",(function(){return V(this).tag})),E(K,"withoutSetter",(function(e){return se(T(e),e)})),O.f=pe,S.f=le,j.f=ue,x.f=de,k.f=_.f=fe,w.f=me,N.f=function(e){return se(I(e),e)},s&&(P($,"description",{configurable:!0,get:function(){return V(this).description}}),c||E(W,"propertyIsEnumerable",pe,{unsafe:!0}))),n({global:!0,constructor:!0,wrap:!0,forced:!l,sham:!l},{Symbol:K}),U(g(oe),(function(e){D(e)})),n({target:F,stat:!0,forced:!l},{useSetter:function(){ie=!0},useSimple:function(){ie=!1}}),n({target:"Object",stat:!0,forced:!l,sham:!s},{create:function(e,t){return void 0===t?b(e):ue(b(e),t)},defineProperty:le,defineProperties:ue,getOwnPropertyDescriptor:de}),n({target:"Object",stat:!0,forced:!l},{getOwnPropertyNames:fe}),R(),M(K,F),C[L]=!0},2733:(e,t,r)=>{"use strict";var n=r(8612),o=r(382),i=r(8389),a=r(4762),c=r(5755),s=r(1483),l=r(4815),u=r(6261),p=r(3864),d=r(6726),f=i.Symbol,m=f&&f.prototype;if(o&&s(f)&&(!("description"in m)||void 0!==f().description)){var v={},y=function(){var e=arguments.length<1||void 0===arguments[0]?void 0:u(arguments[0]),t=l(m,this)?new f(e):void 0===e?f():f(e);return""===e&&(v[t]=!0),t};d(y,f),y.prototype=m,m.constructor=y;var h="Symbol(description detection)"===String(f("description detection")),b=a(m.valueOf),g=a(m.toString),k=/^Symbol\((.*)\)[^)]+$/,_=a("".replace),w=a("".slice);p(m,"description",{configurable:!0,get:function(){var e=b(this);if(c(v,e))return"";var t=g(e),r=h?w(t,7,-1):_(t,k,"$1");return""===r?void 0:r}}),n({global:!0,constructor:!0,forced:!0},{Symbol:y})}},2484:(e,t,r)=>{"use strict";var n=r(8612),o=r(1409),i=r(5755),a=r(6261),c=r(7255),s=r(3218),l=c("string-to-symbol-registry"),u=c("symbol-to-string-registry");n({target:"Symbol",stat:!0,forced:!s},{for:function(e){var t=a(e);if(i(l,t))return l[t];var r=o("Symbol")(t);return l[t]=r,u[r]=t,r}})},4701:(e,t,r)=>{"use strict";r(7849)("iterator")},9305:(e,t,r)=>{"use strict";r(5443),r(2484),r(1894),r(6184),r(7859)},1894:(e,t,r)=>{"use strict";var n=r(8612),o=r(5755),i=r(1423),a=r(8761),c=r(7255),s=r(3218),l=c("symbol-to-string-registry");n({target:"Symbol",stat:!0,forced:!s},{keyFor:function(e){if(!i(e))throw new TypeError(a(e)+" is not a symbol");if(o(l,e))return l[e]}})},1678:(e,t,r)=>{"use strict";var n=r(7849),o=r(8192);n("toPrimitive"),o()},3630:(e,t,r)=>{"use strict";var n=r(8389),o=r(4842),i=r(1902),a=r(4793),c=r(9037),s=function(e){if(e&&e.forEach!==a)try{c(e,"forEach",a)}catch(t){e.forEach=a}};for(var l in o)o[l]&&s(n[l]&&n[l].prototype);s(i)},2367:(e,t,r)=>{"use strict";var n=r(8389),o=r(4842),i=r(1902),a=r(4962),c=r(9037),s=r(2277),l=r(1)("iterator"),u=a.values,p=function(e,t){if(e){if(e[l]!==u)try{c(e,l,u)}catch(t){e[l]=u}if(s(e,t,!0),o[t])for(var r in a)if(e[r]!==a[r])try{c(e,r,a[r])}catch(t){e[r]=a[r]}}};for(var d in o)p(n[d]&&n[d].prototype,d);p(i,"DOMTokenList")},9833:(e,t,r)=>{"use strict";var n=r(8612),o=r(8389),i=r(9570)(o.setInterval,!0);n({global:!0,bind:!0,forced:o.setInterval!==i},{setInterval:i})},3989:(e,t,r)=>{"use strict";var n=r(8612),o=r(8389),i=r(9570)(o.setTimeout,!0);n({global:!0,bind:!0,forced:o.setTimeout!==i},{setTimeout:i})},7089:(e,t,r)=>{"use strict";r(9833),r(3989)}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var i=t[n]={id:n,exports:{}};return e[n].call(i.exports,i,i.exports,r),i.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.nc=void 0;var n={};return(()=>{"use strict";r.r(n),r.d(n,{addEventListener:()=>hn,defaultConfig:()=>pn,defaultTranslations:()=>dn,getConfigTranslations:()=>gn,getElement:()=>yn,getElementID:()=>vn,getManager:()=>An,language:()=>ct,render:()=>_n,renderContextualConsentNotices:()=>wn,resetManagers:()=>Pn,setup:()=>jn,show:()=>On,updateConfig:()=>ar,validateConfig:()=>Sn,version:()=>zn}),r(9305),r(2733),r(4701),r(1678),r(4776),r(4382),r(2084),r(9892),r(4962),r(6584),r(9336),r(4754),r(1908),r(8551),r(94),r(6457),r(8908),r(3810),r(8557),r(5021),r(3687),r(3994),r(3630),r(2367);var e,t,o,i,a,c,s,l,u={},p=[],d=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,f=Array.isArray;function m(e,t){for(var r in t)e[r]=t[r];return e}function v(e){var t=e.parentNode;t&&t.removeChild(e)}function y(t,r,n){var o,i,a,c={};for(a in r)"key"==a?o=r[a]:"ref"==a?i=r[a]:c[a]=r[a];if(arguments.length>2&&(c.children=arguments.length>3?e.call(arguments,2):n),"function"==typeof t&&null!=t.defaultProps)for(a in t.defaultProps)void 0===c[a]&&(c[a]=t.defaultProps[a]);return h(t,c,o,i,null)}function h(e,r,n,i,a){var c={type:e,props:r,key:n,ref:i,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:null==a?++o:a,__i:-1,__u:0};return null==a&&null!=t.vnode&&t.vnode(c),c}function b(e){return e.children}function g(e,t){this.props=e,this.context=t}function k(e,t){if(null==t)return e.__?k(e.__,e.__i+1):null;for(var r;t<e.__k.length;t++)if(null!=(r=e.__k[t])&&null!=r.__e)return r.__e;return"function"==typeof e.type?k(e):null}function _(e,r,n){var o,i=e.__v,a=i.__e,c=e.__P;if(c)return(o=m({},i)).__v=i.__v+1,t.vnode&&t.vnode(o),N(c,o,i,e.__n,void 0!==c.ownerSVGElement,32&i.__u?[a]:null,r,null==a?k(i):a,!!(32&i.__u),n),o.__v=i.__v,o.__.__k[o.__i]=o,o.__d=void 0,o.__e!=a&&w(o),o}function w(e){var t,r;if(null!=(e=e.__)&&null!=e.__c){for(e.__e=e.__c.base=null,t=0;t<e.__k.length;t++)if(null!=(r=e.__k[t])&&null!=r.__e){e.__e=e.__c.base=r.__e;break}return w(e)}}function x(e){(!e.__d&&(e.__d=!0)&&i.push(e)&&!S.__r++||a!==t.debounceRendering)&&((a=t.debounceRendering)||c)(S)}function S(){var e,r,n,o=[],a=[];for(i.sort(s);e=i.shift();)e.__d&&(n=i.length,r=_(e,o,a)||r,0===n||i.length>n?(D(o,r,a),a.length=o.length=0,r=void 0,i.sort(s)):r&&t.__c&&t.__c(r,p));r&&D(o,r,a),S.__r=0}function j(e,t,r,n,o,i,a,c,s,l,d){var f,m,v,y,h,b=n&&n.__k||p,g=t.length;for(r.__d=s,O(r,t,b),s=r.__d,f=0;f<g;f++)null!=(v=r.__k[f])&&"boolean"!=typeof v&&"function"!=typeof v&&(m=-1===v.__i?u:b[v.__i]||u,v.__i=f,N(e,v,m,o,i,a,c,s,l,d),y=v.__e,v.ref&&m.ref!=v.ref&&(m.ref&&M(m.ref,null,v),d.push(v.ref,v.__c||y,v)),null==h&&null!=y&&(h=y),65536&v.__u||m.__k===v.__k?s=E(v,s,e):"function"==typeof v.type&&void 0!==v.__d?s=v.__d:y&&(s=y.nextSibling),v.__d=void 0,v.__u&=-196609);r.__d=s,r.__e=h}function O(e,t,r){var n,o,i,a,c,s=t.length,l=r.length,u=l,p=0;for(e.__k=[],n=0;n<s;n++)a=n+p,null!=(o=e.__k[n]=null==(o=t[n])||"boolean"==typeof o||"function"==typeof o?null:"string"==typeof o||"number"==typeof o||"bigint"==typeof o||o.constructor==String?h(null,o,null,null,null):f(o)?h(b,{children:o},null,null,null):void 0===o.constructor&&o.__b>0?h(o.type,o.props,o.key,o.ref?o.ref:null,o.__v):o)?(o.__=e,o.__b=e.__b+1,c=A(o,r,a,u),o.__i=c,i=null,-1!==c&&(u--,(i=r[c])&&(i.__u|=131072)),null==i||null===i.__v?(-1==c&&p--,"function"!=typeof o.type&&(o.__u|=65536)):c!==a&&(c===a+1?p++:c>a?u>s-a?p+=c-a:p--:c<a?c==a-1&&(p=c-a):p=0,c!==n+p&&(o.__u|=65536))):(i=r[a])&&null==i.key&&i.__e&&0==(131072&i.__u)&&(i.__e==e.__d&&(e.__d=k(i)),q(i,i,!1),r[a]=null,u--);if(u)for(n=0;n<l;n++)null!=(i=r[n])&&0==(131072&i.__u)&&(i.__e==e.__d&&(e.__d=k(i)),q(i,i))}function E(e,t,r){var n,o;if("function"==typeof e.type){for(n=e.__k,o=0;n&&o<n.length;o++)n[o]&&(n[o].__=e,t=E(n[o],t,r));return t}e.__e!=t&&(r.insertBefore(e.__e,t||null),t=e.__e);do{t=t&&t.nextSibling}while(null!=t&&8===t.nodeType);return t}function P(e,t){return t=t||[],null==e||"boolean"==typeof e||(f(e)?e.some((function(e){P(e,t)})):t.push(e)),t}function A(e,t,r,n){var o=e.key,i=e.type,a=r-1,c=r+1,s=t[r];if(null===s||s&&o==s.key&&i===s.type&&0==(131072&s.__u))return r;if(n>(null!=s&&0==(131072&s.__u)?1:0))for(;a>=0||c<t.length;){if(a>=0){if((s=t[a])&&0==(131072&s.__u)&&o==s.key&&i===s.type)return a;a--}if(c<t.length){if((s=t[c])&&0==(131072&s.__u)&&o==s.key&&i===s.type)return c;c++}}return-1}function z(e,t,r){"-"===t[0]?e.setProperty(t,null==r?"":r):e[t]=null==r?"":"number"!=typeof r||d.test(t)?r:r+"px"}function C(e,t,r,n,o){var i;e:if("style"===t)if("string"==typeof r)e.style.cssText=r;else{if("string"==typeof n&&(e.style.cssText=n=""),n)for(t in n)r&&t in r||z(e.style,t,"");if(r)for(t in r)n&&r[t]===n[t]||z(e.style,t,r[t])}else if("o"===t[0]&&"n"===t[1])i=t!==(t=t.replace(/(PointerCapture)$|Capture$/i,"$1")),t=t.toLowerCase()in e?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+i]=r,r?n?r.u=n.u:(r.u=Date.now(),e.addEventListener(t,i?I:T,i)):e.removeEventListener(t,i?I:T,i);else{if(o)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!==t&&"height"!==t&&"href"!==t&&"list"!==t&&"form"!==t&&"tabIndex"!==t&&"download"!==t&&"rowSpan"!==t&&"colSpan"!==t&&"role"!==t&&t in e)try{e[t]=null==r?"":r;break e}catch(e){}"function"==typeof r||(null==r||!1===r&&"-"!==t[4]?e.removeAttribute(t):e.setAttribute(t,r))}}function T(e){if(this.l){var r=this.l[e.type+!1];if(e.t){if(e.t<=r.u)return}else e.t=Date.now();return r(t.event?t.event(e):e)}}function I(e){if(this.l)return this.l[e.type+!0](t.event?t.event(e):e)}function N(e,r,n,o,i,a,c,s,l,u){var p,d,v,y,h,k,_,w,x,S,O,E,P,A,z,C=r.type;if(void 0!==r.constructor)return null;128&n.__u&&(l=!!(32&n.__u),a=[s=r.__e=n.__e]),(p=t.__b)&&p(r);e:if("function"==typeof C)try{if(w=r.props,x=(p=C.contextType)&&o[p.__c],S=p?x?x.props.value:p.__:o,n.__c?_=(d=r.__c=n.__c).__=d.__E:("prototype"in C&&C.prototype.render?r.__c=d=new C(w,S):(r.__c=d=new g(w,S),d.constructor=C,d.render=U),x&&x.sub(d),d.props=w,d.state||(d.state={}),d.context=S,d.__n=o,v=d.__d=!0,d.__h=[],d._sb=[]),null==d.__s&&(d.__s=d.state),null!=C.getDerivedStateFromProps&&(d.__s==d.state&&(d.__s=m({},d.__s)),m(d.__s,C.getDerivedStateFromProps(w,d.__s))),y=d.props,h=d.state,d.__v=r,v)null==C.getDerivedStateFromProps&&null!=d.componentWillMount&&d.componentWillMount(),null!=d.componentDidMount&&d.__h.push(d.componentDidMount);else{if(null==C.getDerivedStateFromProps&&w!==y&&null!=d.componentWillReceiveProps&&d.componentWillReceiveProps(w,S),!d.__e&&(null!=d.shouldComponentUpdate&&!1===d.shouldComponentUpdate(w,d.__s,S)||r.__v===n.__v)){for(r.__v!==n.__v&&(d.props=w,d.state=d.__s,d.__d=!1),r.__e=n.__e,r.__k=n.__k,r.__k.forEach((function(e){e&&(e.__=r)})),O=0;O<d._sb.length;O++)d.__h.push(d._sb[O]);d._sb=[],d.__h.length&&c.push(d);break e}null!=d.componentWillUpdate&&d.componentWillUpdate(w,d.__s,S),null!=d.componentDidUpdate&&d.__h.push((function(){d.componentDidUpdate(y,h,k)}))}if(d.context=S,d.props=w,d.__P=e,d.__e=!1,E=t.__r,P=0,"prototype"in C&&C.prototype.render){for(d.state=d.__s,d.__d=!1,E&&E(r),p=d.render(d.props,d.state,d.context),A=0;A<d._sb.length;A++)d.__h.push(d._sb[A]);d._sb=[]}else do{d.__d=!1,E&&E(r),p=d.render(d.props,d.state,d.context),d.state=d.__s}while(d.__d&&++P<25);d.state=d.__s,null!=d.getChildContext&&(o=m(m({},o),d.getChildContext())),v||null==d.getSnapshotBeforeUpdate||(k=d.getSnapshotBeforeUpdate(y,h)),j(e,f(z=null!=p&&p.type===b&&null==p.key?p.props.children:p)?z:[z],r,n,o,i,a,c,s,l,u),d.base=r.__e,r.__u&=-161,d.__h.length&&c.push(d),_&&(d.__E=d.__=null)}catch(e){r.__v=null,l||null!=a?(r.__e=s,r.__u|=l?160:32,a[a.indexOf(s)]=null):(r.__e=n.__e,r.__k=n.__k),t.__e(e,r,n)}else null==a&&r.__v===n.__v?(r.__k=n.__k,r.__e=n.__e):r.__e=R(n.__e,r,n,o,i,a,c,l,u);(p=t.diffed)&&p(r)}function D(e,r,n){for(var o=0;o<n.length;o++)M(n[o],n[++o],n[++o]);t.__c&&t.__c(r,e),e.some((function(r){try{e=r.__h,r.__h=[],e.some((function(e){e.call(r)}))}catch(e){t.__e(e,r.__v)}}))}function R(t,r,n,o,i,a,c,s,l){var p,d,m,y,h,b,g,_=n.props,w=r.props,x=r.type;if("svg"===x&&(i=!0),null!=a)for(p=0;p<a.length;p++)if((h=a[p])&&"setAttribute"in h==!!x&&(x?h.localName===x:3===h.nodeType)){t=h,a[p]=null;break}if(null==t){if(null===x)return document.createTextNode(w);t=i?document.createElementNS("http://www.w3.org/2000/svg",x):document.createElement(x,w.is&&w),a=null,s=!1}if(null===x)_===w||s&&t.data===w||(t.data=w);else{if(a=a&&e.call(t.childNodes),_=n.props||u,!s&&null!=a)for(_={},p=0;p<t.attributes.length;p++)_[(h=t.attributes[p]).name]=h.value;for(p in _)h=_[p],"children"==p||("dangerouslySetInnerHTML"==p?m=h:"key"===p||p in w||C(t,p,null,h,i));for(p in w)h=w[p],"children"==p?y=h:"dangerouslySetInnerHTML"==p?d=h:"value"==p?b=h:"checked"==p?g=h:"key"===p||s&&"function"!=typeof h||_[p]===h||C(t,p,h,_[p],i);if(d)s||m&&(d.__html===m.__html||d.__html===t.innerHTML)||(t.innerHTML=d.__html),r.__k=[];else if(m&&(t.innerHTML=""),j(t,f(y)?y:[y],r,n,o,i&&"foreignObject"!==x,a,c,a?a[0]:n.__k&&k(n,0),s,l),null!=a)for(p=a.length;p--;)null!=a[p]&&v(a[p]);s||(p="value",void 0!==b&&(b!==t[p]||"progress"===x&&!b||"option"===x&&b!==_[p])&&C(t,p,b,_[p],!1),p="checked",void 0!==g&&g!==t[p]&&C(t,p,g,_[p],!1))}return t}function M(e,r,n){try{"function"==typeof e?e(r):e.current=r}catch(e){t.__e(e,n)}}function q(e,r,n){var o,i;if(t.unmount&&t.unmount(e),(o=e.ref)&&(o.current&&o.current!==e.__e||M(o,null,r)),null!=(o=e.__c)){if(o.componentWillUnmount)try{o.componentWillUnmount()}catch(e){t.__e(e,r)}o.base=o.__P=null,e.__c=void 0}if(o=e.__k)for(i=0;i<o.length;i++)o[i]&&q(o[i],r,n||"function"!=typeof e.type);n||null==e.__e||v(e.__e),e.__=e.__e=e.__d=void 0}function U(e,t,r){return this.constructor(e,r)}function L(r,n,o){var i,a,c,s;t.__&&t.__(r,n),a=(i="function"==typeof o)?null:o&&o.__k||n.__k,c=[],s=[],N(n,r=(!i&&o||n).__k=y(b,null,[r]),a||u,u,void 0!==n.ownerSVGElement,!i&&o?[o]:a?null:n.firstChild?e.call(n.childNodes):null,c,!i&&o?o:a?a.__e:n.firstChild,i,s),r.__d=void 0,D(c,r,s)}function F(e,t){L(e,t,F)}function B(t,r,n){var o,i,a,c,s=m({},t.props);for(a in t.type&&t.type.defaultProps&&(c=t.type.defaultProps),r)"key"==a?o=r[a]:"ref"==a?i=r[a]:s[a]=void 0===r[a]&&void 0!==c?c[a]:r[a];return arguments.length>2&&(s.children=arguments.length>3?e.call(arguments,2):n),h(t.type,s,o||t.key,i||t.ref,null)}e=p.slice,t={__e:function(e,t,r,n){for(var o,i,a;t=t.__;)if((o=t.__c)&&!o.__)try{if((i=o.constructor)&&null!=i.getDerivedStateFromError&&(o.setState(i.getDerivedStateFromError(e)),a=o.__d),null!=o.componentDidCatch&&(o.componentDidCatch(e,n||{}),a=o.__d),a)return o.__E=o}catch(t){e=t}throw e}},o=0,g.prototype.setState=function(e,t){var r;r=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=m({},this.state),"function"==typeof e&&(e=e(m({},r),this.props)),e&&m(r,e),null!=e&&this.__v&&(t&&this._sb.push(t),x(this))},g.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),x(this))},g.prototype.render=b,i=[],c="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,s=function(e,t){return e.__v.__b-t.__v.__b},S.__r=0,l=0;var H,V,W,K,$=0,G=[],Z=[],X=t,Y=X.__b,J=X.__r,Q=X.diffed,ee=X.__c,te=X.unmount,re=X.__;function ne(e,t){X.__h&&X.__h(V,e,$||t),$=0;var r=V.__H||(V.__H={__:[],__h:[]});return e>=r.__.length&&r.__.push({__V:Z}),r.__[e]}function oe(e){return $=1,ie(ve,e)}function ie(e,t,r){var n=ne(H++,2);if(n.t=e,!n.__c&&(n.__=[r?r(t):ve(void 0,t),function(e){var t=n.__N?n.__N[0]:n.__[0],r=n.t(t,e);t!==r&&(n.__N=[r,n.__[1]],n.__c.setState({}))}],n.__c=V,!V.u)){var o=function(e,t,r){if(!n.__c.__H)return!0;var o=n.__c.__H.__.filter((function(e){return!!e.__c}));if(o.every((function(e){return!e.__N})))return!i||i.call(this,e,t,r);var a=!1;return o.forEach((function(e){if(e.__N){var t=e.__[0];e.__=e.__N,e.__N=void 0,t!==e.__[0]&&(a=!0)}})),!(!a&&n.__c.props===e)&&(!i||i.call(this,e,t,r))};V.u=!0;var i=V.shouldComponentUpdate,a=V.componentWillUpdate;V.componentWillUpdate=function(e,t,r){if(this.__e){var n=i;i=void 0,o(e,t,r),i=n}a&&a.call(this,e,t,r)},V.shouldComponentUpdate=o}return n.__N||n.__}function ae(e,t){var r=ne(H++,3);!X.__s&&me(r.__H,t)&&(r.__=e,r.i=t,V.__H.__h.push(r))}function ce(e,t){var r=ne(H++,4);!X.__s&&me(r.__H,t)&&(r.__=e,r.i=t,V.__h.push(r))}function se(e,t){var r=ne(H++,7);return me(r.__H,t)?(r.__V=e(),r.i=t,r.__h=e,r.__V):r.__}function le(){for(var e;e=G.shift();)if(e.__P&&e.__H)try{e.__H.__h.forEach(de),e.__H.__h.forEach(fe),e.__H.__h=[]}catch(t){e.__H.__h=[],X.__e(t,e.__v)}}X.__b=function(e){V=null,Y&&Y(e)},X.__=function(e,t){e&&t.__k&&t.__k.__m&&(e.__m=t.__k.__m),re&&re(e,t)},X.__r=function(e){J&&J(e),H=0;var t=(V=e.__c).__H;t&&(W===V?(t.__h=[],V.__h=[],t.__.forEach((function(e){e.__N&&(e.__=e.__N),e.__V=Z,e.__N=e.i=void 0}))):(t.__h.forEach(de),t.__h.forEach(fe),t.__h=[],H=0)),W=V},X.diffed=function(e){Q&&Q(e);var t=e.__c;t&&t.__H&&(t.__H.__h.length&&(1!==G.push(t)&&K===X.requestAnimationFrame||((K=X.requestAnimationFrame)||pe)(le)),t.__H.__.forEach((function(e){e.i&&(e.__H=e.i),e.__V!==Z&&(e.__=e.__V),e.i=void 0,e.__V=Z}))),W=V=null},X.__c=function(e,t){t.some((function(e){try{e.__h.forEach(de),e.__h=e.__h.filter((function(e){return!e.__||fe(e)}))}catch(r){t.some((function(e){e.__h&&(e.__h=[])})),t=[],X.__e(r,e.__v)}})),ee&&ee(e,t)},X.unmount=function(e){te&&te(e);var t,r=e.__c;r&&r.__H&&(r.__H.__.forEach((function(e){try{de(e)}catch(e){t=e}})),r.__H=void 0,t&&X.__e(t,r.__v))};var ue="function"==typeof requestAnimationFrame;function pe(e){var t,r=function(){clearTimeout(n),ue&&cancelAnimationFrame(t),setTimeout(e)},n=setTimeout(r,100);ue&&(t=requestAnimationFrame(r))}function de(e){var t=V,r=e.__c;"function"==typeof r&&(e.__c=void 0,r()),V=t}function fe(e){var t=V;e.__c=e.__(),V=t}function me(e,t){return!e||e.length!==t.length||t.some((function(t,r){return t!==e[r]}))}function ve(e,t){return"function"==typeof t?t(e):t}function ye(e,t){for(var r in t)e[r]=t[r];return e}function he(e,t){for(var r in e)if("__source"!==r&&!(r in t))return!0;for(var n in t)if("__source"!==n&&e[n]!==t[n])return!0;return!1}function be(e,t){this.props=e,this.context=t}(be.prototype=new g).isPureReactComponent=!0,be.prototype.shouldComponentUpdate=function(e,t){return he(this.props,e)||he(this.state,t)};var ge=t.__b;t.__b=function(e){e.type&&e.type.__f&&e.ref&&(e.props.ref=e.ref,e.ref=null),ge&&ge(e)};var ke="undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.forward_ref")||3911,_e=function(e,t){return null==e?null:P(P(e).map(t))},we={map:_e,forEach:_e,count:function(e){return e?P(e).length:0},only:function(e){var t=P(e);if(1!==t.length)throw"Children.only";return t[0]},toArray:P},xe=t.__e;t.__e=function(e,t,r,n){if(e.then)for(var o,i=t;i=i.__;)if((o=i.__c)&&o.__c)return null==t.__e&&(t.__e=r.__e,t.__k=r.__k),o.__c(e,t);xe(e,t,r,n)};var Se=t.unmount;function je(e,t,r){return e&&(e.__c&&e.__c.__H&&(e.__c.__H.__.forEach((function(e){"function"==typeof e.__c&&e.__c()})),e.__c.__H=null),null!=(e=ye({},e)).__c&&(e.__c.__P===r&&(e.__c.__P=t),e.__c=null),e.__k=e.__k&&e.__k.map((function(e){return je(e,t,r)}))),e}function Oe(e,t,r){return e&&r&&(e.__v=null,e.__k=e.__k&&e.__k.map((function(e){return Oe(e,t,r)})),e.__c&&e.__c.__P===t&&(e.__e&&r.appendChild(e.__e),e.__c.__e=!0,e.__c.__P=r)),e}function Ee(){this.__u=0,this.t=null,this.__b=null}function Pe(e){var t=e.__.__c;return t&&t.__a&&t.__a(e)}function Ae(){this.u=null,this.o=null}t.unmount=function(e){var t=e.__c;t&&t.__R&&t.__R(),t&&32&e.__u&&(e.type=null),Se&&Se(e)},(Ee.prototype=new g).__c=function(e,t){var r=t.__c,n=this;null==n.t&&(n.t=[]),n.t.push(r);var o=Pe(n.__v),i=!1,a=function(){i||(i=!0,r.__R=null,o?o(c):c())};r.__R=a;var c=function(){if(! --n.__u){if(n.state.__a){var e=n.state.__a;n.__v.__k[0]=Oe(e,e.__c.__P,e.__c.__O)}var t;for(n.setState({__a:n.__b=null});t=n.t.pop();)t.forceUpdate()}};n.__u++||32&t.__u||n.setState({__a:n.__b=n.__v.__k[0]}),e.then(a,a)},Ee.prototype.componentWillUnmount=function(){this.t=[]},Ee.prototype.render=function(e,t){if(this.__b){if(this.__v.__k){var r=document.createElement("div"),n=this.__v.__k[0].__c;this.__v.__k[0]=je(this.__b,r,n.__O=n.__P)}this.__b=null}var o=t.__a&&y(b,null,e.fallback);return o&&(o.__u&=-33),[y(b,null,t.__a?null:e.children),o]};var ze=function(e,t,r){if(++r[1]===r[0]&&e.o.delete(t),e.props.revealOrder&&("t"!==e.props.revealOrder[0]||!e.o.size))for(r=e.u;r;){for(;r.length>3;)r.pop()();if(r[1]<r[0])break;e.u=r=r[2]}};function Ce(e){return this.getChildContext=function(){return e.context},e.children}function Te(e){var t=this,r=e.i;t.componentWillUnmount=function(){L(null,t.l),t.l=null,t.i=null},t.i&&t.i!==r&&t.componentWillUnmount(),t.l||(t.i=r,t.l={nodeType:1,parentNode:r,childNodes:[],appendChild:function(e){this.childNodes.push(e),t.i.appendChild(e)},insertBefore:function(e,r){this.childNodes.push(e),t.i.appendChild(e)},removeChild:function(e){this.childNodes.splice(this.childNodes.indexOf(e)>>>1,1),t.i.removeChild(e)}}),L(y(Ce,{context:t.context},e.__v),t.l)}(Ae.prototype=new g).__a=function(e){var t=this,r=Pe(t.__v),n=t.o.get(e);return n[0]++,function(o){var i=function(){t.props.revealOrder?(n.push(o),ze(t,e,n)):o()};r?r(i):i()}},Ae.prototype.render=function(e){this.u=null,this.o=new Map;var t=P(e.children);e.revealOrder&&"b"===e.revealOrder[0]&&t.reverse();for(var r=t.length;r--;)this.o.set(t[r],this.u=[1,0,this.u]);return e.children},Ae.prototype.componentDidUpdate=Ae.prototype.componentDidMount=function(){var e=this;this.o.forEach((function(t,r){ze(e,r,t)}))};var Ie="undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103,Ne=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image(!S)|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,De=/^on(Ani|Tra|Tou|BeforeInp|Compo)/,Re=/[A-Z0-9]/g,Me="undefined"!=typeof document,qe=function(e){return("undefined"!=typeof Symbol&&"symbol"==typeof Symbol()?/fil|che|rad/:/fil|che|ra/).test(e)};function Ue(e,t,r){return null==t.__k&&(t.textContent=""),L(e,t),"function"==typeof r&&r(),e?e.__c:null}g.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach((function(e){Object.defineProperty(g.prototype,e,{configurable:!0,get:function(){return this["UNSAFE_"+e]},set:function(t){Object.defineProperty(this,e,{configurable:!0,writable:!0,value:t})}})}));var Le=t.event;function Fe(){}function Be(){return this.cancelBubble}function He(){return this.defaultPrevented}t.event=function(e){return Le&&(e=Le(e)),e.persist=Fe,e.isPropagationStopped=Be,e.isDefaultPrevented=He,e.nativeEvent=e};var Ve,We={enumerable:!1,configurable:!0,get:function(){return this.class}},Ke=t.vnode;t.vnode=function(e){"string"==typeof e.type&&function(e){var t=e.props,r=e.type,n={};for(var o in t){var i=t[o];if(!("value"===o&&"defaultValue"in t&&null==i||Me&&"children"===o&&"noscript"===r||"class"===o||"className"===o)){var a=o.toLowerCase();"defaultValue"===o&&"value"in t&&null==t.value?o="value":"download"===o&&!0===i?i="":"translate"===a&&"no"===i?i=!1:"ondoubleclick"===a?o="ondblclick":"onchange"!==a||"input"!==r&&"textarea"!==r||qe(t.type)?"onfocus"===a?o="onfocusin":"onblur"===a?o="onfocusout":De.test(o)?o=a:-1===r.indexOf("-")&&Ne.test(o)?o=o.replace(Re,"-$&").toLowerCase():null===i&&(i=void 0):a=o="oninput","oninput"===a&&n[o=a]&&(o="oninputCapture"),n[o]=i}}"select"==r&&n.multiple&&Array.isArray(n.value)&&(n.value=P(t.children).forEach((function(e){e.props.selected=-1!=n.value.indexOf(e.props.value)}))),"select"==r&&null!=n.defaultValue&&(n.value=P(t.children).forEach((function(e){e.props.selected=n.multiple?-1!=n.defaultValue.indexOf(e.props.value):n.defaultValue==e.props.value}))),t.class&&!t.className?(n.class=t.class,Object.defineProperty(n,"className",We)):(t.className&&!t.class||t.class&&t.className)&&(n.class=n.className=t.className),e.props=n}(e),e.$$typeof=Ie,Ke&&Ke(e)};var $e=t.__r;t.__r=function(e){$e&&$e(e),Ve=e.__c};var Ge=t.diffed;t.diffed=function(e){Ge&&Ge(e);var t=e.props,r=e.__e;null!=r&&"textarea"===e.type&&"value"in t&&t.value!==r.value&&(r.value=null==t.value?"":t.value),Ve=null};var Ze={ReactCurrentDispatcher:{current:{readContext:function(e){return Ve.__n[e.__c].props.value}}}};function Xe(e){return!!e&&e.$$typeof===Ie}function Ye(e){e()}function Je(e){var t,r,n=e.v,o=e.__;try{var i=n();return!((t=o)===(r=i)&&(0!==t||1/t==1/r)||t!=t&&r!=r)}catch(e){return!0}}var Qe={useState:oe,useId:function(){var e=ne(H++,11);if(!e.__){for(var t=V.__v;null!==t&&!t.__m&&null!==t.__;)t=t.__;var r=t.__m||(t.__m=[0,0]);e.__="P"+r[0]+"-"+r[1]++}return e.__},useReducer:ie,useEffect:ae,useLayoutEffect:ce,useInsertionEffect:ce,useTransition:function(){return[!1,Ye]},useDeferredValue:function(e){return e},useSyncExternalStore:function(e,t){var r=t(),n=oe({h:{__:r,v:t}}),o=n[0].h,i=n[1];return ce((function(){o.__=r,o.v=t,Je(o)&&i({h:o})}),[e,r,t]),ae((function(){return Je(o)&&i({h:o}),e((function(){Je(o)&&i({h:o})}))}),[e]),r},startTransition:Ye,useRef:function(e){return $=5,se((function(){return{current:e}}),[])},useImperativeHandle:function(e,t,r){$=6,ce((function(){return"function"==typeof e?(e(t()),function(){return e(null)}):e?(e.current=t(),function(){return e.current=null}):void 0}),null==r?r:r.concat(e))},useMemo:se,useCallback:function(e,t){return $=8,se((function(){return e}),t)},useContext:function(e){var t=V.context[e.__c],r=ne(H++,9);return r.c=e,t?(null==r.__&&(r.__=!0,t.sub(V)),t.props.value):e.__},useDebugValue:function(e,t){X.useDebugValue&&X.useDebugValue(t?t(e):e)},version:"17.0.2",Children:we,render:Ue,hydrate:function(e,t,r){return F(e,t),"function"==typeof r&&r(),e?e.__c:null},unmountComponentAtNode:function(e){return!!e.__k&&(L(null,e),!0)},createPortal:function(e,t){var r=y(Te,{__v:e,i:t});return r.containerInfo=t,r},createElement:y,createContext:function(e,t){var r={__c:t="__cC"+l++,__:e,Consumer:function(e,t){return e.children(t)},Provider:function(e){var r,n;return this.getChildContext||(r=[],(n={})[t]=this,this.getChildContext=function(){return n},this.shouldComponentUpdate=function(e){this.props.value!==e.value&&r.some((function(e){e.__e=!0,x(e)}))},this.sub=function(e){r.push(e);var t=e.componentWillUnmount;e.componentWillUnmount=function(){r.splice(r.indexOf(e),1),t&&t.call(e)}}),e.children}};return r.Provider.__=r.Consumer.contextType=r},createFactory:function(e){return y.bind(null,e)},cloneElement:function(e){return Xe(e)?B.apply(null,arguments):e},createRef:function(){return{current:null}},Fragment:b,isValidElement:Xe,isElement:Xe,isFragment:function(e){return Xe(e)&&e.type===b},findDOMNode:function(e){return e&&(e.base||1===e.nodeType&&e)||null},Component:g,PureComponent:be,memo:function(e,t){function r(e){var r=this.props.ref,n=r==e.ref;return!n&&r&&(r.call?r(null):r.current=null),t?!t(this.props,e)||!n:he(this.props,e)}function n(t){return this.shouldComponentUpdate=r,y(e,t)}return n.displayName="Memo("+(e.displayName||e.name)+")",n.prototype.isReactComponent=!0,n.__f=!0,n},forwardRef:function(e){function t(t){var r=ye({},t);return delete r.ref,e(r,t.ref||null)}return t.$$typeof=ke,t.render=t,t.prototype.isReactComponent=t.__f=!0,t.displayName="ForwardRef("+(e.displayName||e.name)+")",t},flushSync:function(e,t){return e(t)},unstable_batchedUpdates:function(e,t){return e(t)},StrictMode:b,Suspense:Ee,SuspenseList:Ae,lazy:function(e){var t,r,n;function o(o){if(t||(t=e()).then((function(e){r=e.default||e}),(function(e){n=e})),n)throw n;if(!r)throw t;return y(r,o)}return o.displayName="Lazy",o.__f=!0,o},__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:Ze},et=(r(6437),r(2697),r(1359),r(6216),r(6448),r(7089),r(2688)),tt=function(e){var t=e.t;return Qe.createElement("svg",{role:"img","aria-label":t(["close"]),width:"12",height:"12",version:"1.1",xmlns:"http://www.w3.org/2000/svg"},Qe.createElement("title",null,t(["close"])),Qe.createElement("line",{x1:"1",y1:"11",x2:"11",y2:"1",strokeWidth:"1"}),Qe.createElement("line",{x1:"1",y1:"1",x2:"11",y2:"11",strokeWidth:"1"}))};function rt(e){return e.split("-").map((function(e){return e.slice(0,1).toUpperCase()+e.slice(1)})).join(" ")}function nt(e){return function(e){if(Array.isArray(e))return ot(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return ot(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?ot(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ot(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function it(e){return it="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},it(e)}tt.propTypes={t:r.n(et)().func},r(7575),r(646),r(987),r(3819);var at=function(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];var o,i=it(r[0]);o=0===r.length?{}:"string"===i||"number"===i?Array.prototype.slice.call(r):r[0];for(var a=[],c=e.toString();c.length>0;){var s=c.match(/\{(?!\{)([\w\d]+)\}(?!\})/);if(null!==s){var l=c.substr(0,s.index);c=c.substr(s.index+s[0].length);var u=parseInt(s[1]);a.push(l),u!=u?a.push(o[s[1]]):a.push(o[u])}else a.push(c),c=""}return a};function ct(e){if(void 0!==e&&void 0!==e.lang&&"zz"!==e.lang)return e.lang;var t=(("string"==typeof window.language?window.language:null)||document.documentElement.lang||(void 0!==e&&void 0!==e.languages&&void 0!==e.languages[0]?e.languages[0]:"en")).toLowerCase(),r=new RegExp("^([\\w]+)-([\\w]+)$").exec(t);return null===r?t:r[1]}function st(e,t,r){var n=t;Array.isArray(n)||(n=[n]);for(var o=e,i=0;i<n.length;i++){if(void 0===o)return r;if(void 0!==n[i]&&n[i].endsWith("?")){var a,c=n[i].slice(0,n[i].length-1);void 0!==(a=o instanceof Map?o.get(c):o[c])&&"string"==typeof a&&(o=a)}else o=o instanceof Map?o.get(n[i]):o[n[i]]}return void 0===o||"string"!=typeof o?r:""!==o?o:void 0}function lt(e,t,r,n){var o=n,i=!1;"!"===o[0]&&(o=o.slice(1),i=!0),Array.isArray(o)||(o=[o]);var a=st(e,[t].concat(nt(o)));if(void 0===a&&void 0!==r&&(a=st(e,[r].concat(nt(o)))),void 0===a){if(i)return;return["[missing translation: ".concat(t,"/").concat(o.join("/"),"]")]}for(var c=arguments.length,s=new Array(c>4?c-4:0),l=4;l<c;l++)s[l-4]=arguments[l];return s.length>0?at.apply(void 0,[a].concat(s)):a}const ut=function(e){var t=e.text,r=e.config;if(t instanceof Array||(t=[t]),!0===r.htmlTexts){var n=!1;"<"===t[0][0]&&(n=!0);var o=t.map((function(e,t){return"string"==typeof e?Qe.createElement("span",{key:t,dangerouslySetInnerHTML:{__html:e}}):e}));return n?Qe.createElement(Qe.Fragment,null,o):Qe.createElement("span",null,o)}return Qe.createElement("span",null,t)};function pt(e){return pt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},pt(e)}function dt(){return dt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},dt.apply(this,arguments)}function ft(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,mt(n.key),n)}}function mt(e){var t=function(e,t){if("object"!=pt(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,"string");if("object"!=pt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==pt(t)?t:String(t)}function vt(e,t,r){return t=ht(t),function(e,t){if(t&&("object"===pt(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,yt()?Reflect.construct(t,r||[],ht(e).constructor):t.apply(e,r))}function yt(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(yt=function(){return!!e})()}function ht(e){return ht=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},ht(e)}function bt(e,t){return bt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},bt(e,t)}var gt=function(e){function t(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),vt(this,t,arguments)}var r,n;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&bt(e,t)}(t,e),r=t,n=[{key:"render",value:function(){var e,t=this.props,r=t.checked,n=t.onlyRequiredEnabled,o=t.onToggle,i=t.name,a=t.lang,c=t.config,s=t.translations,l=t.title,u=t.description,p=t.visible,d=t.t,f=this.props.required||!1,m=this.props.optOut||!1,v=this.props.purposes||[],y="service-item-".concat(i),h="".concat(y,"-title"),b=v.map((function(e){return d(["!","purposes",e,"title?"])||rt(e)})).join(", "),g=m?Qe.createElement("span",{className:"cm-opt-out",title:d(["service","optOut","description"])},d(["service","optOut","title"])):"",k=f?Qe.createElement("span",{className:"cm-required",title:d(["service","required","description"])},d(["service","required","title"])):"";v.length>0&&(e=Qe.createElement("p",{className:"purposes"},d(["service",v.length>1?"purposes":"purpose"]),": ",b));var _=u||lt(s,a,"zz",["!","description"])||d(["!",i,"description?"]);return Qe.createElement("div",null,Qe.createElement("input",{id:y,className:"cm-list-input"+(f?" required":"")+(n?" half-checked only-required":""),"aria-labelledby":"".concat(h),"aria-describedby":"".concat(y,"-description"),disabled:f,checked:r||f,tabIndex:p?"0":"-1",type:"checkbox",onChange:function(e){o(e.target.checked)}}),Qe.createElement("label",dt({htmlFor:y,className:"cm-list-label"},f?{tabIndex:"0"}:{}),Qe.createElement("span",{className:"cm-list-title",id:"".concat(h)},l||lt(s,a,"zz",["!","title"])||d(["!",i,"title?"])||rt(i)),k,g,Qe.createElement("span",{className:"cm-switch"},Qe.createElement("div",{className:"slider round active"}))),Qe.createElement("div",{id:"".concat(y,"-description")},_&&Qe.createElement("p",{className:"cm-list-description"},Qe.createElement(ut,{config:c,text:_})),e))}}],n&&ft(r.prototype,n),Object.defineProperty(r,"prototype",{writable:!1}),t}(Qe.Component);function kt(e){return kt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},kt(e)}function _t(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,wt(n.key),n)}}function wt(e){var t=function(e,t){if("object"!=kt(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,"string");if("object"!=kt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==kt(t)?t:String(t)}function xt(e,t,r){return t=jt(t),function(e,t){if(t&&("object"===kt(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Ot(e)}(e,St()?Reflect.construct(t,r||[],jt(e).constructor):t.apply(e,r))}function St(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(St=function(){return!!e})()}function jt(e){return jt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},jt(e)}function Ot(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Et(e,t){return Et=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Et(e,t)}function Pt(){return Pt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Pt.apply(this,arguments)}var At=function(e){var t=e.services,r=e.config,n=e.consents,o=e.lang,i=e.toggle,a=e.visible,c=e.t;return t.map((function(e){var t=n[e.name];return Qe.createElement("li",{key:e.name,className:"cm-service"},Qe.createElement(gt,Pt({checked:t||e.required,onToggle:function(t){i([e],t)},config:r,lang:o,visible:a,t:c},e)))}))},zt=function(e){function t(e){var r;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),r=xt(this,t,[e]),e.manager.watch(Ot(r)),r.state={consents:e.manager.consents},r}var r,n;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Et(e,t)}(t,e),r=t,(n=[{key:"componentWillUnmount",value:function(){this.props.manager.unwatch(this)}},{key:"update",value:function(e,t,r){e===this.props.manager&&"consents"===t&&this.setState({consents:r})}},{key:"render",value:function(){var e=this.props,t=e.config,r=e.t,n=e.manager,o=e.lang,i=this.state.consents,a=t.services,c=function(e,t){e.map((function(e){e.required||n.updateConsent(e.name,t)}))},s=Qe.createElement(At,{config:t,lang:o,services:a,t:r,consents:i,toggle:c}),l=a.filter((function(e){return!e.required})),u=l.filter((function(e){return i[e.name]})).length,p=a.filter((function(e){return e.required})).length,d=u===l.length;return a.filter((function(e){return e.required})).length,Qe.createElement("ul",{className:"cm-services"},s,!t.hideToggleAll&&l.length>1&&Qe.createElement("li",{className:"cm-service cm-toggle-all"},Qe.createElement(gt,{name:"disableAll",title:r(["service","disableAll","title"]),description:r(["service","disableAll","description"]),checked:d,config:t,onlyRequiredEnabled:!d&&p>0,onToggle:function(e){c(a,e)},lang:o,t:r})))}}])&&_t(r.prototype,n),Object.defineProperty(r,"prototype",{writable:!1}),t}(Qe.Component);function Ct(e){return Ct="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ct(e)}function Tt(){return Tt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Tt.apply(this,arguments)}function It(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Nt(n.key),n)}}function Nt(e){var t=function(e,t){if("object"!=Ct(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,"string");if("object"!=Ct(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==Ct(t)?t:String(t)}function Dt(e,t,r){return t=Mt(t),function(e,t){if(t&&("object"===Ct(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,Rt()?Reflect.construct(t,r||[],Mt(e).constructor):t.apply(e,r))}function Rt(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(Rt=function(){return!!e})()}function Mt(e){return Mt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Mt(e)}function qt(e,t){return qt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},qt(e,t)}var Ut=function(e){function t(e){var r;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),(r=Dt(this,t,[e])).state={servicesVisible:!1},r}var r,n;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&qt(e,t)}(t,e),r=t,n=[{key:"render",value:function(){var e,t=this,r=this.props,n=r.allEnabled,o=r.onlyRequiredEnabled,i=r.allDisabled,a=r.services,c=r.config,s=r.onToggle,l=r.name,u=r.lang,p=r.manager,d=r.consents,f=r.title,m=r.description,v=r.t,y=this.state.servicesVisible,h=this.props.required||!1,b=this.props.purposes||[],g="purpose-item-".concat(l),k="".concat(g,"-title"),_=b.map((function(e){return v(["!","purposes",e,"title?"])||rt(e)})).join(", "),w=h?Qe.createElement("span",{className:"cm-required",title:v(["!","service","required","description"])||""},v(["service","required","title"])):"";b.length>0&&(e=Qe.createElement("p",{className:"purposes"},v(["purpose",b.length>1?"purposes":"purpose"]),": ",_));var x=function(e){e.preventDefault();var r="false"!==e.currentTarget.getAttribute("aria-expanded");e.currentTarget.setAttribute("aria-expanded",!r),t.setState({servicesVisible:!y})},S=Qe.createElement(At,{config:c,lang:u,services:a,toggle:function(e,t){e.map((function(e){e.required||p.updateConsent(e.name,t)}))},consents:d,visible:y,t:v}),j=m||v(["!","purposes",l,"description"]);return Qe.createElement(Qe.Fragment,null,Qe.createElement("input",{id:g,className:"cm-list-input"+(h?" required":"")+(n?"":o?" only-required":" half-checked"),"aria-labelledby":"".concat(k),"aria-describedby":"".concat(g,"-description"),disabled:h,checked:n||!i&&!o,type:"checkbox",onChange:function(e){s(e.target.checked)}}),Qe.createElement("label",Tt({htmlFor:g,className:"cm-list-label"},h?{tabIndex:"0"}:{}),Qe.createElement("span",{className:"cm-list-title",id:"".concat(k)},f||v(["!","purposes",l,"title?"])||rt(l)),w,Qe.createElement("span",{className:"cm-switch"},Qe.createElement("div",{className:"slider round active"}))),Qe.createElement("div",{id:"".concat(g,"-description")},j&&Qe.createElement("p",{className:"cm-list-description"},Qe.createElement(ut,{config:c,text:j})),e),a.length>0&&Qe.createElement("div",{className:"cm-services"},Qe.createElement("div",{className:"cm-caret"},Qe.createElement("a",{href:"#","aria-haspopup":"true","aria-expanded":"false",tabIndex:"0",onClick:x,onKeyDown:function(e){32===e.keyCode&&x(e)}},y&&Qe.createElement("span",null,"↑")||Qe.createElement("span",null,"↓")," ",a.length," ",v(["purposeItem",a.length>1?"services":"service"]))),Qe.createElement("ul",{className:"cm-content"+(y?" expanded":"")},S)))}}],n&&It(r.prototype,n),Object.defineProperty(r,"prototype",{writable:!1}),t}(Qe.Component);function Lt(e){return Lt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Lt(e)}function Ft(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(e){if("string"==typeof e)return Bt(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Bt(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,c=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return a=e.done,e},e:function(e){c=!0,i=e},f:function(){try{a||null==r.return||r.return()}finally{if(c)throw i}}}}function Bt(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Ht(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Vt(n.key),n)}}function Vt(e){var t=function(e,t){if("object"!=Lt(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,"string");if("object"!=Lt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==Lt(t)?t:String(t)}function Wt(e,t,r){return t=$t(t),function(e,t){if(t&&("object"===Lt(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Gt(e)}(e,Kt()?Reflect.construct(t,r||[],$t(e).constructor):t.apply(e,r))}function Kt(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(Kt=function(){return!!e})()}function $t(e){return $t=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},$t(e)}function Gt(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Zt(e,t){return Zt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Zt(e,t)}var Xt=function(e){function t(e){var r;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),r=Wt(this,t,[e]),e.manager.watch(Gt(r)),r.state={consents:e.manager.consents},r}var r,n;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Zt(e,t)}(t,e),r=t,n=[{key:"componentWillUnmount",value:function(){this.props.manager.unwatch(this)}},{key:"update",value:function(e,t,r){e===this.props.manager&&"consents"===t&&this.setState({consents:r})}},{key:"render",value:function(){var e,t=this.props,r=t.config,n=t.t,o=t.manager,i=t.lang,a=this.state.consents,c=r.services,s={},l=Ft(c);try{for(l.s();!(e=l.n()).done;){var u,p=e.value,d=Ft(p.purposes);try{for(d.s();!(u=d.n()).done;){var f=u.value;void 0===s[f]&&(s[f]=[]),s[f].push(p)}}catch(e){d.e(e)}finally{d.f()}}}catch(e){l.e(e)}finally{l.f()}var m=function(e,t){e.map((function(e){var r,n=Ft(s[e]);try{for(n.s();!(r=n.n()).done;){var i=r.value;i.required||o.updateConsent(i.name,t)}}catch(e){n.e(e)}finally{n.f()}}))},v=function(e){var t,r={allEnabled:!0,onlyRequiredEnabled:!0,allDisabled:!0,allRequired:!0},n=Ft(e);try{for(n.s();!(t=n.n()).done;){var o=t.value;o.required||(r.allRequired=!1),a[o.name]?(o.required||(r.onlyRequiredEnabled=!1),r.allDisabled=!1):o.required||(r.allEnabled=!1)}}catch(e){n.e(e)}finally{n.f()}return r.allDisabled&&(r.onlyRequiredEnabled=!1),r},y=r.purposeOrder||[],h=Object.keys(s).sort((function(e,t){return y.indexOf(e)-y.indexOf(t)})).map((function(e){var t=v(s[e]);return Qe.createElement("li",{key:e,className:"cm-purpose"},Qe.createElement(Ut,{allEnabled:t.allEnabled,allDisabled:t.allDisabled,onlyRequiredEnabled:t.onlyRequiredEnabled,required:t.allRequired,consents:a,name:e,config:r,lang:i,manager:o,onToggle:function(t){m([e],t)},services:s[e],t:n}))})),b=Object.keys(s).filter((function(e){var t,r=Ft(s[e]);try{for(r.s();!(t=r.n()).done;)if(!t.value.required)return!0}catch(e){r.e(e)}finally{r.f()}return!1})),g=v(c);return Qe.createElement("ul",{className:"cm-purposes"},h,b.length>1&&Qe.createElement("li",{className:"cm-purpose cm-toggle-all"},Qe.createElement(Ut,{name:"disableAll",title:n(["service","disableAll","title"]),description:n(["service","disableAll","description"]),allDisabled:g.allDisabled,allEnabled:g.allEnabled,onlyRequiredEnabled:g.onlyRequiredEnabled,onToggle:function(e){m(Object.keys(s),e)},manager:o,consents:a,config:r,lang:i,services:[],t:n})))}}],n&&Ht(r.prototype,n),Object.defineProperty(r,"prototype",{writable:!1}),t}(Qe.Component);function Yt(e){return Yt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Yt(e)}function Jt(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Qt(n.key),n)}}function Qt(e){var t=function(e,t){if("object"!=Yt(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,"string");if("object"!=Yt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==Yt(t)?t:String(t)}function er(e,t,r){return t=rr(t),function(e,t){if(t&&("object"===Yt(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,tr()?Reflect.construct(t,r||[],rr(e).constructor):t.apply(e,r))}function tr(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(tr=function(){return!!e})()}function rr(e){return rr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},rr(e)}function nr(e,t){return nr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},nr(e,t)}var or=function(e){function t(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),er(this,t,arguments)}var r,n;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&nr(e,t)}(t,e),r=t,(n=[{key:"componentDidMount",value:function(){this.consentModalRef&&this.consentModalRef.focus()}},{key:"render",value:function(){var e,t,r,n=this,o=this.props,i=o.hide,a=o.confirming,c=o.saveAndHide,s=o.acceptAndHide,l=o.declineAndHide,u=o.config,p=o.manager,d=o.lang,f=o.t,m=u.embedded,v=void 0===u.groupByPurpose||u.groupByPurpose;u.mustConsent||(e=Qe.createElement("button",{title:f(["close"]),"aria-label":f(["close"]),className:"hide",type:"button",onClick:i,tabIndex:"0",ref:function(e){n.consentModalRef=e}},Qe.createElement(tt,{t:f}))),u.hideDeclineAll||p.confirmed||(t=Qe.createElement("button",{disabled:a,className:"cm-btn cm-btn-decline cm-btn-danger cn-decline",type:"button",onClick:l},f(["decline"])));var y,h,b,g=Qe.createElement("button",{disabled:a,className:"cm-btn cm-btn-success cm-btn-info cm-btn-accept",type:"button",onClick:c},f([p.confirmed?"save":"acceptSelected"]));u.acceptAll&&!p.confirmed&&(r=Qe.createElement("button",{disabled:a,className:"cm-btn cm-btn-success cm-btn-accept-all",type:"button",onClick:s},f(["acceptAll"]))),void 0!==u.privacyPolicy?"string"==typeof u.privacyPolicy?y=u.privacyPolicy:"object"===Yt(u.privacyPolicy)&&(y=u.privacyPolicy[d]||u.privacyPolicy.default):void 0!==(y=f(["!","privacyPolicyUrl"],{lang:d}))&&(y=y.join("")),void 0!==y&&(h=Qe.createElement("a",{key:"ppLink",href:y,target:"_blank",rel:"noopener"},f(["privacyPolicy","name"]))),b=v?Qe.createElement(Xt,{t:f,config:u,manager:p,lang:d}):Qe.createElement(zt,{t:f,config:u,manager:p,lang:d});var k=Qe.createElement("div",{className:"cm-modal cm-klaro"},Qe.createElement("div",{className:"cm-header"},e,Qe.createElement("h1",{className:"title"},Qe.createElement(ut,{config:u,text:f(["consentModal","title"])})),Qe.createElement("p",null,Qe.createElement(ut,{config:u,text:[f(["consentModal","description"])].concat(h&&[" "].concat(f(["privacyPolicy","text"],{privacyPolicy:h}))||[])}))),Qe.createElement("div",{className:"cm-body"},b),Qe.createElement("div",{className:"cm-footer"},Qe.createElement("div",{className:"cm-footer-buttons"},t,g,r),!u.disablePoweredBy&&Qe.createElement("p",{className:"cm-powered-by"},Qe.createElement("a",{target:"_blank",href:u.poweredBy||"https://kiprotect.com/klaro",rel:"noopener"},f(["poweredBy"])))));return m?Qe.createElement("div",{id:"cookieScreen",className:"cookie-modal cm-embedded"},k):Qe.createElement("div",{id:"cookieScreen",className:"cookie-modal"},Qe.createElement("div",{className:"cm-bg",onClick:i}),k)}}])&&Jt(r.prototype,n),Object.defineProperty(r,"prototype",{writable:!1}),t}(Qe.Component);function ir(e){return ir="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ir(e)}function ar(e,t,r){void 0===r&&(r=!0);for(var n=Object.keys(t),o=0;o<n.length;o++){var i=n[o],a=t[i],c=e[i];"string"==typeof a?(r||void 0===c)&&(e[i]=a):"object"===ir(a)&&("object"===ir(c)?ar(c,a,r):(r||void 0===c)&&(e[i]=a))}return e}function cr(e){return cr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},cr(e)}function sr(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function lr(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,yr(n.key),n)}}function ur(e,t,r){return t=dr(t),function(e,t){if(t&&("object"===cr(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return fr(e)}(e,pr()?Reflect.construct(t,r||[],dr(e).constructor):t.apply(e,r))}function pr(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(pr=function(){return!!e})()}function dr(e){return dr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},dr(e)}function fr(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function mr(e,t){return mr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},mr(e,t)}function vr(e,t,r){return(t=yr(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function yr(e){var t=function(e,t){if("object"!=cr(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,"string");if("object"!=cr(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==cr(t)?t:String(t)}r(2745);var hr=function(e){function t(e){var r;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),vr(fr(r=ur(this,t,[e])),"executeButtonClicked",(function(e,t,n){var o=r.state.modal,i=0;e&&(i=r.props.manager.changeAll(t));var a=r.props.manager.confirmed;if(r.props.manager.saveAndApplyConsents(n),e&&!a&&(o||r.props.config.mustConsent)){var c=function(){r.setState({confirming:!1}),r.props.hide()};r.setState({confirming:!0}),0===i?c():setTimeout(c,800)}else r.props.hide()})),vr(fr(r),"saveAndHide",(function(){r.executeButtonClicked(!1,!1,"save")})),vr(fr(r),"acceptAndHide",(function(){r.executeButtonClicked(!0,!0,"accept")})),vr(fr(r),"declineAndHide",(function(){r.executeButtonClicked(!0,!1,"decline")})),r.state={modal:e.modal,confirming:!1},r}var r,n;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&mr(e,t)}(t,e),r=t,n=[{key:"componentDidUpdate",value:function(e){e.modal!==this.props.modal&&this.setState({modal:this.props.modal}),this.noticeRef&&this.noticeRef.focus()}},{key:"render",value:function(){var e,t,r,n=this,o=this.props,i=o.lang,a=o.config,c=o.show,s=o.manager,l=o.testing,u=o.t,p=this.state,d=p.confirming,f=p.modal,m=a.embedded,v=a.noticeAsModal,y=a.hideLearnMore,h=a.purposeOrder||[],b=function(e){for(var t=new Set([]),r=0;r<e.services.length;r++)for(var n=e.services[r].purposes||[],o=0;o<n.length;o++)t.add(n[o]);return Array.from(t)}(a).filter((function(e){return"functional"!==e})).sort((function(e,t){return h.indexOf(e)-h.indexOf(t)})),g=b.map((function(e){return u(["!","purposes",e,"title?"])||rt(e)}));t=1===g.length?g[0]:[].concat((r=g.slice(0,-2),function(e){if(Array.isArray(e))return sr(e)}(r)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(r)||function(e,t){if(e){if("string"==typeof e)return sr(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?sr(e,t):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),[g.slice(-2).join(" & ")]).join(", "),void 0!==a.privacyPolicy?"string"==typeof a.privacyPolicy?e=a.privacyPolicy:"object"===cr(a.privacyPolicy)&&(e=a.privacyPolicy[i]||a.privacyPolicy.default):void 0!==(e=u(["!","privacyPolicyUrl"],{lang:i}))&&(e=e.join(""));var k,_=function(e){e.preventDefault(),n.setState({modal:!0})};if(s.changed&&(k=Qe.createElement("p",{className:"cn-changes"},u(["consentNotice","changeDescription"]))),!c&&!l&&!d)return Qe.createElement("div",null);var w,x=(!a.mustConsent||v)&&!s.confirmed&&!a.noNotice,S=a.hideDeclineAll?"":Qe.createElement("button",{className:"cm-btn cm-btn-danger cn-decline",type:"button",onClick:this.declineAndHide},u(["decline"])),j=a.acceptAll?Qe.createElement("button",{className:"cm-btn cm-btn-success",type:"button",onClick:this.acceptAndHide},u(["ok"])):Qe.createElement("button",{className:"cm-btn cm-btn-success",type:"button",onClick:this.saveAndHide},u(["ok"])),O=function(){return v?Qe.createElement("button",{key:"learnMoreLink",className:"cm-btn cm-btn-lern-more cm-btn-info",type:"button",onClick:_},u(["consentNotice","learnMore"])):Qe.createElement("a",{key:"learnMoreLink",className:"cm-link cn-learn-more",href:"#",onClick:_},u(["consentNotice","learnMore"]))};if(void 0!==e&&(w=Qe.createElement("a",{key:"ppLink",href:e},u(["privacyPolicy","name"]))),f||s.confirmed&&!l||!s.confirmed&&a.mustConsent)return Qe.createElement(or,{t:u,lang:i,config:a,hide:function(){a.mustConsent&&!a.acceptAll||(s.confirmed&&!l?n.props.hide():n.setState({modal:!1}),setTimeout((function(){n.noticeRef&&n.noticeRef.focus()}),1))},confirming:d,declineAndHide:this.declineAndHide,saveAndHide:this.saveAndHide,acceptAndHide:this.acceptAndHide,manager:s});var E=Qe.createElement("div",{role:"dialog","aria-describedby":"id-cookie-notice","aria-labelledby":"id-cookie-title",id:"klaro-cookie-notice",tabIndex:"0",autofocus:a.autoFocus,ref:function(e){n.noticeRef=e},className:"cookie-notice ".concat(x||l?"":"cookie-notice-hidden"," ").concat(v?"cookie-modal-notice":""," ").concat(m?"cn-embedded":"")},Qe.createElement("div",{className:"cn-body"},u(["!","consentNotice","title"])&&a.showNoticeTitle&&Qe.createElement("h2",{id:"id-cookie-title"},u(["consentNotice","title"])),Qe.createElement("p",{id:"id-cookie-notice"},Qe.createElement(ut,{config:a,text:u(["consentNotice","description"],{purposes:Qe.createElement("strong",{key:"strong"},t),privacyPolicy:w,learnMoreLink:O()})})),l&&Qe.createElement("p",null,u(["consentNotice","testing"])),k,Qe.createElement("div",{className:"cn-ok"},!y&&O(),Qe.createElement("div",{className:"cn-buttons"},S,j))));return v?Qe.createElement("div",{id:"cookieScreen",className:"cookie-modal"},Qe.createElement("div",{className:"cm-bg"}),E):E}}],n&&lr(r.prototype,n),Object.defineProperty(r,"prototype",{writable:!1}),t}(Qe.Component);function br(e){return br="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},br(e)}function gr(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,kr(n.key),n)}}function kr(e){var t=function(e,t){if("object"!=br(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,"string");if("object"!=br(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==br(t)?t:String(t)}function _r(e,t,r){return t=xr(t),function(e,t){if(t&&("object"===br(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Sr(e)}(e,wr()?Reflect.construct(t,r||[],xr(e).constructor):t.apply(e,r))}function wr(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(wr=function(){return!!e})()}function xr(e){return xr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},xr(e)}function Sr(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function jr(e,t){return jr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},jr(e,t)}var Or=function(e){function t(e){var r;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),r=_r(this,t,[e]),e.manager.watch(Sr(r)),r.state={show:e.show>0||!e.manager.confirmed},r}var r,n;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&jr(e,t)}(t,e),r=t,n=[{key:"componentWillUnmount",value:function(){this.props.manager.unwatch(this)}},{key:"update",value:function(e,t){e===this.props.manager&&"applyConsents"===t&&(!this.props.config.embedded&&this.props.manager.confirmed?this.setState({show:!1}):this.forceUpdate())}},{key:"notifyApi",value:function(){var e=this.props,t=e.api,r=e.modal,n=e.show,o=e.config;if(void 0!==t){if(r||n>0)return;this.props.manager.confirmed||this.props.manager.auxiliaryStore.getWithKey("shown-before")||(t.update(this,"showNotice",{config:o}),this.props.manager.auxiliaryStore.setWithKey("shown-before",!0))}}},{key:"componentDidMount",value:function(){this.notifyApi()}},{key:"componentDidUpdate",value:function(e){if(e.show!==this.props.show){this.notifyApi();var t=this.props.show>0||!this.props.manager.confirmed;t!==this.state.show&&this.setState({show:t})}}},{key:"render",value:function(){var e=this,t=this.props,r=t.config,n=t.t,o=t.lang,i=t.testing,a=t.manager,c=t.modal,s=this.state.show,l=r.additionalClass,u=r.embedded,p=r.stylePrefix;return Qe.createElement("div",{lang:o,className:(p||"klaro")+(void 0!==l?" "+l:"")},Qe.createElement(hr,{key:"app-"+this.props.show,t:n,testing:i,show:s,lang:o,modal:c,hide:function(){u||e.setState({show:!1})},config:r,manager:a}))}}],n&&gr(r.prototype,n),Object.defineProperty(r,"prototype",{writable:!1}),t}(Qe.Component);function Er(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}const Pr=function(e){var t=e.manager,r=e.style,n=e.config,o=e.t,i=e.lang,a=e.service,c=function(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],s=!0,l=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=i.call(r)).done)&&(c.push(n.value),c.length!==t);s=!0);}catch(e){l=!0,o=e}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(e,t)||function(e,t){if(e){if("string"==typeof e)return Er(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Er(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(oe(0),2),s=c[0],l=c[1],u=n.additionalClass,p=(n.embedded,n.stylePrefix);ae((function(){var e={update:function(){return l(s+1)}};return t.watch(e),function(){t.unwatch(e)}}));var d=lt(a.translations||{},i,"zz",["!","title"])||o(["!",a.name,"title?"])||rt(a.name);return Qe.createElement("div",{lang:i,className:(p||"klaro")+(void 0!==u?" "+u:"")+" cm-as-context-notice"},Qe.createElement("div",{className:"context-notice"+(void 0!==r?" cm-".concat(r):"")},Qe.createElement("p",null,o(["contextualConsent","description"],{title:d})),Qe.createElement("p",{className:"cm-buttons"},Qe.createElement("button",{className:"cm-btn cm-btn-success",type:"button",onClick:function(){t.updateConsent(a.name,!0),t.applyConsents(!1,!0,a.name),t.updateConsent(a.name,!1)}},o(["contextualConsent","acceptOnce"])),null!==t.store.get()?Qe.createElement("button",{className:"cm-btn cm-btn-success-var",type:"button",onClick:function(){t.updateConsent(a.name,!0),t.confirmed?(t.saveConsents("contextual-accept"),t.applyConsents(!1,!0,a.name)):t.applyConsents(!1,!0,a.name)}},o(["contextualConsent","acceptAlways"])):""),null===t.store.get()&&n.showDescriptionEmptyStore?Qe.createElement(Qe.Fragment,null,Qe.createElement("p",{className:"ccn-description-empty-store"},o(["contextualConsent","descriptionEmptyStore"],{title:d,link:Qe.createElement("a",{key:"modalLink",className:"ccn-modal-link",href:"#",onClick:function(e){e.preventDefault(),On(n,!0)}},o(["contextualConsent","modalLinkText"]))}))):""))};var Ar=r(2690);function zr(e){return zr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},zr(e)}function Cr(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Tr(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Cr(Object(r),!0).forEach((function(t){var n,o,i;n=e,o=t,i=r[t],(o=Nr(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Cr(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Ir(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Nr(n.key),n)}}function Nr(e){var t=function(e,t){if("object"!=zr(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,"string");if("object"!=zr(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==zr(t)?t:String(t)}r(76);var Dr=function(){function e(t,r,n){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.url=t,this.id=r,this.opts=Object.assign({},n)}var t,r;return t=e,(r=[{key:"getLocationData",value:function(e){var t=e.records||{};return{pathname:void 0===t.savePathname||t.savePathname?location.pathname:void 0,port:""!==location.port?parseInt(location.port):0,hostname:location.hostname,protocol:location.protocol.slice(0,location.protocol.length-1)}}},{key:"getUserData",value:function(){return{client_version:zn(),client_name:"klaro:web"}}},{key:"getBaseConsentData",value:function(e){return{location_data:this.getLocationData(e),user_data:this.getUserData(e)}}},{key:"update",value:function(e,t,r){if("saveConsents"===t){if("save"===r.type&&0===Object.keys(r.changes).length)return;var n=Tr(Tr({},this.getBaseConsentData(e.config)),{},{consent_data:{consents:r.consents,changes:"save"===r.type?r.changes:void 0,type:r.type,config:e.config.id}});this.submitConsentData(n)}else if("showNotice"===t){var o=Tr(Tr({},this.getBaseConsentData(r.config)),{},{consent_data:{consents:{},changes:{},type:"show",config:r.config.id}});this.submitConsentData(o)}}},{key:"apiRequest",value:function(e,t,r,n){var o=this;return new Promise((function(i,a){var c,s,l=new XMLHttpRequest;l.addEventListener("load",(function(){var e=JSON.parse(l.response);l.status<200||l.status>=300?(e.status=l.status,a(e)):i(e,l.status)})),l.addEventListener("error",(function(){a({status:0,xhr:l})})),void 0!==r&&("GET"===e?t+="?"+(s=r,"?"+Object.keys(s).map((function(e){return e+"="+encodeURIComponent(s[e])})).join("&")):c=JSON.stringify(r)),l.open(e,o.url+t),void 0!==c&&l.setRequestHeader("Content-Type",n||"application/json;charset=UTF-8"),l.send(c)}))}},{key:"submitConsentData",value:function(e){return this.apiRequest("POST","/v1/privacy-managers/"+this.id+"/submit",e,"text/plain;charset=UTF-8")}},{key:"loadConfig",value:function(e){return this.apiRequest("GET","/v1/privacy-managers/"+this.id+"/config.json?name="+e+(this.opts.testing?"&testing=true":""))}},{key:"loadConfigs",value:function(){return this.apiRequest("GET","/v1/privacy-managers/"+this.id+"/configs.json"+(this.opts.testing?"&testing=true":""))}}])&&Ir(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),e}(),Rr=(r(7132),r(4062),r(5482));function Mr(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],s=!0,l=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=i.call(r)).done)&&(c.push(n.value),c.length!==t);s=!0);}catch(e){l=!0,o=e}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(e,t)||qr(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function qr(e,t){if(e){if("string"==typeof e)return Ur(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Ur(e,t):void 0}}function Ur(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Lr(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Fr(e){for(var t=new Map([]),r=0,n=Object.keys(e);r<n.length;r++){var o=n[r],i=e[o];"string"==typeof o&&("string"==typeof i||null===i?t.set(o,i):t.set(o,Fr(i)))}return t}function Br(e,t,r,n){var o=function(e,t,r){if(r instanceof Map){var n=new Map([]);Br(n,r,!0,!1),e.set(t,n)}else e.set(t,r)};if(!(t instanceof Map&&e instanceof Map))throw new Error("Parameters are not maps!");void 0===r&&(r=!0),void 0===n&&(n=!1),n&&(e=new e.constructor(e));var i,a=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(e){if("string"==typeof e)return Lr(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Lr(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,c=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return a=e.done,e},e:function(e){c=!0,i=e},f:function(){try{a||null==r.return||r.return()}finally{if(c)throw i}}}}(t.keys());try{for(a.s();!(i=a.n()).done;){var c=i.value,s=t.get(c),l=e.get(c);if(e.has(c))if(s instanceof Map&&l instanceof Map)e.set(c,Br(l,s,r,n));else{if(!r)continue;o(e,c,s)}else o(e,c,s)}}catch(e){a.e(e)}finally{a.f()}return e}var Hr={top:{_meta:{incompatibleWith:["bottom"]},"notice-top":"20px","notice-bottom":"auto"},bottom:{_meta:{incompatibleWith:["top"]},"notice-bottom":"20px","notice-top":"auto"},left:{_meta:{incompatibleWith:["wide"]},"notice-left":"20px","notice-right":"auto"},right:{_meta:{incompatibleWith:["wide"]},"notice-right":"20px","notice-left":"auto"},wide:{"notice-left":"20px","notice-right":"auto","notice-max-width":"calc(100vw - 60px)","notice-position":"fixed"},light:{"button-text-color":"#fff",dark1:"#fafafa",dark2:"#777",dark3:"#555",light1:"#444",light2:"#666",light3:"#111",green3:"#f00"}},Vr=r(5292),Wr=r.n(Vr),Kr=r(9893),$r=r.n(Kr),Gr=r(9383),Zr=r.n(Gr),Xr=r(6884),Yr=r.n(Xr),Jr=r(9088),Qr=r.n(Jr),en=r(7997),tn=r.n(en),rn=r(679),nn={};function on(e){return on="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},on(e)}function an(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function cn(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?an(Object(r),!0).forEach((function(t){var n,o,i;n=e,o=t,i=r[t],o=function(e){var t=function(e,t){if("object"!=on(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,"string");if("object"!=on(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==on(t)?t:String(t)}(o),o in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):an(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function sn(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=ln(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,c=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return a=e.done,e},e:function(e){c=!0,i=e},f:function(){try{a||null==r.return||r.return()}finally{if(c)throw i}}}}function ln(e,t){if(e){if("string"==typeof e)return un(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?un(e,t):void 0}}function un(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}nn.styleTagTransform=tn(),nn.setAttributes=Yr(),nn.insert=Zr().bind(null,"head"),nn.domAPI=$r(),nn.insertStyleElement=Qr(),Wr()(rn.A,nn),rn.A&&rn.A.locals&&rn.A.locals;var pn,dn=new Map([]),fn={},mn={};function vn(e,t){return(e.elementID||"klaro")+(t?"-ide":"")}function yn(e,t){var r=vn(e,t),n=document.getElementById(r);return null===n&&((n=document.createElement("div")).id=r,document.body.appendChild(n)),n}function hn(e,t){if(void 0===fn[e]?fn[e]=[t]:fn[e].push(t),void 0!==mn[e]){var r,n=sn(mn[e]);try{for(n.s();!(r=n.n()).done;){var o=r.value;if(!1===t.apply(void 0,function(e){if(Array.isArray(e))return un(e)}(i=o)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(i)||ln(i)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()))break}}catch(e){n.e(e)}finally{n.f()}}var i}function bn(e){for(var t=fn[e],r=arguments.length,n=new Array(r>1?r-1:0),o=1;o<r;o++)n[o-1]=arguments[o];if(void 0===mn[e]?mn[e]=[n]:mn[e].push(n),void 0!==t){var i,a=sn(t);try{for(a.s();!(i=a.n()).done;)if(!0===i.value.apply(void 0,n))return!0}catch(e){a.e(e)}finally{a.f()}}}function gn(e){var t=new Map([]);return Br(t,dn),Br(t,Fr(e.translations||{})),t}var kn=1;function _n(e,t){if(void 0!==e){t=t||{},bn("render",e=Sn(e),t);var r=0;t.show&&(r=kn++);var n=yn(e),o=An(e);void 0!==t.api&&o.watch(t.api),function(e,t,r){if(void 0!==e.styling){var n=Object.assign({},e.styling);if(void 0!==n.theme){var o=n.theme;o instanceof Array||(o=[o]),n={};var i,a=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=qr(e))){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,c=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return a=e.done,e},e:function(e){c=!0,i=e},f:function(){try{a||null==r.return||r.return()}finally{if(c)throw i}}}}(o);try{for(a.s();!(i=a.n()).done;){var c=t[i.value];if(void 0!==c)for(var s=0,l=Object.entries(c);s<l.length;s++){var u=Mr(l[s],2),p=u[0],d=u[1];p.startsWith("_")||(n[p]=d)}}}catch(e){a.e(e)}finally{a.f()}for(var f=0,m=Object.entries(e.styling);f<m.length;f++){var v=Mr(m[f],2),y=v[0],h=v[1];"theme"!==y&&(n[y]=h)}}void 0===r&&(r=document.documentElement);for(var b=0,g=Object.entries(n);b<g.length;b++){var k=Mr(g[b],2),_=k[0],w=k[1];r.style.setProperty("--"+_,w)}window.document.documentMode&&r===document.documentElement&&(0,Rr.N3)(n)}}(e,Hr,n);var i=ct(e),a=gn(e),c=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return lt.apply(void 0,[a,i,e.fallbackLang||"zz"].concat(r))},s=Ue(Qe.createElement(Or,{t:c,lang:i,manager:o,config:e,testing:t.testing,modal:t.modal,api:t.api,show:r}),n);return wn(o,c,i,e,t),s}}function wn(e,t,r,n,o){var i,a=[],c=sn(n.services);try{for(c.s();!(i=c.n()).done;){var s,l=i.value,u=e.getConsent(l.name)&&e.confirmed,p=sn(document.querySelectorAll("[data-name='"+l.name+"']"));try{for(p.s();!(s=p.n()).done;){var d=s.value,f=(0,Rr.RT)(d);if("placeholder"!==f.type&&("IFRAME"===d.tagName||"DIV"===d.tagName)){var m=d.previousElementSibling;if(null!==m){var v=(0,Rr.RT)(m);"placeholder"===v.type&&v.name===l.name||(m=null)}if(null===m){(m=document.createElement("DIV")).style.maxWidth=d.width+"px",m.style.height=d.height+"px",(0,Rr.X7)({type:"placeholder",name:l.name},m),u&&(m.style.display="none"),d.parentElement.insertBefore(m,d);var y=Ue(Qe.createElement(Pr,{t,lang:r,manager:e,config:n,service:l,style:f.style,testing:o.testing,api:o.api}),m);a.push(y)}"IFRAME"===d.tagName&&(f.src=d.src),void 0===f["modified-by-klaro"]&&void 0===d.style.display&&(f["original-display"]=d.style.display),f["modified-by-klaro"]="yes",(0,Rr.X7)(f,d),u||(d.src="",d.style.display="none")}}}catch(e){p.e(e)}finally{p.f()}}}catch(e){c.e(e)}finally{c.f()}return a}function xn(e){/complete|interactive|loaded/.test(document.readyState)?e():window.addEventListener("DOMContentLoaded",e)}function Sn(e){var t=cn({},e);return 2===t.version||(void 0!==t.apps&&void 0===t.services&&(t.services=t.apps,console.warn("Warning, your configuration file is outdated. Please change `apps` to `services`"),delete t.apps),void 0!==t.translations&&void 0!==t.translations.apps&&void 0===t.services&&(t.translations.services=t.translations.apps,console.warn("Warning, your configuration file is outdated. Please change `apps` to `services` in the `translations` key"),delete t.translations.apps)),t}function jn(e){if(void 0!==window){var t=(0,Rr.XZ)("klaro"),r=new Map(decodeURI(location.hash.slice(1)).split("&").map((function(e){return e.split("=")})).map((function(e){return 1===e.length?[e[0],!0]:e}))),n=r.get("klaro-testing"),o=function(e){var t=cn(cn({},e),{},{testing:n});pn.noAutoLoad||pn.testing&&!t.testing||_n(pn,t)};if(void 0!==e)pn=e,xn((function(){return o({})}));else if(null!==t){var i=function(e){var t=e.getAttribute("data-klaro-id");if(null!==t)return t;var r=/.*\/privacy-managers\/([a-f0-9]+)\/klaro.*\.js/.exec(e.src);return null!==r?r[1]:null}(t),a=function(e){var t=e.getAttribute("data-klaro-api-url");if(null!==t)return t;var r=/(http(?:s)?:\/\/[^/]+)\/v1\/privacy-managers\/([a-f0-9]+)\/klaro.*\.js/.exec(e.src);return null!==r?r[1]:null}(t),c=function(e,t){if(e.has("klaro-config"))return e.get("klaro-config");var r=t.getAttribute("data-klaro-config");return null!==r?r:"default"}(r,t);if(null!==i){var s=new Dr(a,i,{testing:n});if(void 0!==window.klaroApiConfigs){if(!0===bn("apiConfigsLoaded",window.klaroApiConfigs,s))return;var l=window.klaroApiConfigs.find((function(e){return e.name===c&&("active"===e.status||n)}));void 0!==l?(pn=l,xn((function(){return o({api:s})}))):bn("apiConfigsFailed",{})}else s.loadConfig(c).then((function(e){!0!==bn("apiConfigsLoaded",[e],s)&&(pn=e,xn((function(){return o({api:s})})))})).catch((function(e){console.error(e,"cannot load Klaro configs"),bn("apiConfigsFailed",e)}))}else{var u=t.getAttribute("data-klaro-config")||"klaroConfig";void 0!==(pn=window[u])&&xn((function(){return o({})}))}}r.has("klaro-ide")&&function(e){var t=/^(.*)(\/[^/]+)$/.exec(e.src)[1]||"",r=document.createElement("script");r.src=""!==t?t+"/ide.js":"ide.js",r.type="application/javascript";var n,o=sn(r.attributes);try{for(o.s();!(n=o.n()).done;){var i=n.value;r.setAttribute(i.name,i.value)}}catch(e){o.e(e)}finally{o.f()}document.head.appendChild(r)}(t)}}function On(e,t,r){return _n(e=e||pn,{show:!0,modal:t,api:r}),!1}var En={};function Pn(){for(var e in Object.keys(En))delete En[e]}function An(e){var t=(e=e||pn).storageName||e.cookieName||"default";return void 0===En[t]&&(En[t]=new Ar.default(Sn(e))),En[t]}function zn(){return"v"==="v0.7.22"[0]?"v0.7.22".slice(1):"v0.7.22"}var Cn=Fr({ca:{acceptAll:"Accepta-les totes",acceptSelected:"Accepta les escollides",service:{disableAll:{description:"Useu aquest botó per a habilitar o deshabilitar totes les aplicacions.",title:"Habilita/deshabilita totes les aplicacions"},optOut:{description:"Aquesta aplicació es carrega per defecte, però podeu desactivar-la",title:"(opt-out)"},purpose:"Finalitat",purposes:"Finalitats",required:{description:"Aquesta aplicació es necessita sempre",title:"(necessària)"}},close:"Tanca",consentModal:{description:"Aquí podeu veure i personalitzar la informació que recopilem sobre vós.",privacyPolicy:{name:"política de privadesa",text:"Per a més informació, consulteu la nostra {privacyPolicy}."},title:"Informació que recopilem"},consentNotice:{changeDescription:"Hi ha hagut canvis des de la vostra darrera visita. Actualitzeu el vostre consentiment.",description:"Recopilem i processem la vostra informació personal amb les següents finalitats: {purposes}.",imprint:{name:"Empremta"},learnMore:"Saber-ne més",privacyPolicy:{name:"política de privadesa"}},decline:"Rebutja",ok:"Accepta",poweredBy:"Funciona amb Klaro!",purposeItem:{service:"aplicació",services:"aplicacions"},save:"Desa"},cs:{privacyPolicy:{name:"zásady ochrany soukromí",text:'Pro další informace si přečtete naše <tr-hint v="privacy policy">{privacyPolicy}</tr-hint>.'},consentModal:{title:"Služby, které bychom rádi využili",description:"Zde můžete posoudit a přizpůsobit služby, které bychom rádi na tomto webu používali. Máte to pod kontrolou! Povolte nebo zakažte služby, jak uznáte za vhodné."},consentNotice:{testing:"Testing mode!",changeDescription:"Od vaší poslední návštěvy došlo ke změnám, obnovte prosím svůj souhlas.",description:"„Dobrý den! Můžeme povolit některé další služby pro {purposes}? Svůj souhlas můžete kdykoliv změnit nebo odvolat.“","learnMore|capitalize":"Vyberu si"},účely:{functional:{"title|capitalize":"Poskytování služeb",description:"Tyto služby jsou nezbytné pro správné fungování tohoto webu. Nelze je zde deaktivovat, protože služba by jinak nefungovala správně.\n"},performance:{"title|capitalize":"Optimalizace výkonu",description:"V rámci těchto služeb jsou zpracovávány osobní údaje za účelem optimalizace služeb, které jsou na tomto webu poskytovány.\n"},marketing:{"title|capitalize":"Marketing",description:"V rámci těchto služeb jsou zpracovávány osobní údaje, aby se vám zobrazoval relevantní obsah o produktech, službách nebo tématech, které by vás mohly zajímat."},advertising:{"title|capitalize":"Reklama",description:"V rámci těchto služeb jsou zpracovávány osobní údaje, aby vám zobrazovaly personalizované nebo zájmově orientované reklamy."}},purposeItem:{service:"Jednoduchá služba <tr-snip></tr-snip> , kterou nainstaluji do svého počítače.",services:"Několik jednoduchých služeb <tr-snip></tr-snip> , které nainstaluji do svého počítače."},"ok|capitalize":"To je v pořádku",save:"uložit","decline|capitalize":"Nepřijímám",close:"zavřít",acceptAll:"přijmout vše",acceptSelected:"přijmout vybrané",service:{disableAll:{title:"povolit nebo zakázat všechny služby",description:"Pomocí tohoto přepínače můžete povolit nebo zakázat všechny služby."},optOut:{title:"(opt-out)",description:"Tato služba se načítá ve výchozím nastavení (ale můžete ji zrušit)"},required:{title:"(vždy vyžadováno)",description:"Tato služba je vždy vyžadována"},purposes:"Zpracování  pro účely <tr-snip></tr-snip>",purpose:"Zpracování pro účely <tr-snip></tr-snip>"},poweredBy:"Realizováno pomocí Klaro!",contextualConsent:{description:"Chcete načíst externí obsah dodávaný prostřednictvím {title}?",acceptOnce:"Ano",acceptAlways:"Vždy"}},da:{acceptAll:"Tillad alle",acceptSelected:"Tillad udvalgte",service:{disableAll:{description:"Brug denne kontakt til at aktivere/deaktivere alle apps.",title:"Aktiver/deaktiver alle applikatione"},optOut:{description:"Denne applikation indlæses som standard (men du kan deaktivere den)",title:"Opt-Out"},purpose:"Formål",purposes:"Formål",required:{description:"Denne applikation er altid nødvendig",title:"(Altid nødvendig)"}},close:"Luk",consentModal:{description:"Her kan du se og ændre, hvilke informationer vi gemmer om dig.",privacyPolicy:{name:"Flere informationer finde du under {privacyPolicy}",text:"databeskyttelseserklæring."},title:"Informationer, som vi gemmer"},consentNotice:{changeDescription:"Der har været ændringer siden dit sidste besøg. Opdater dit valg.",description:"Vi gemmer og behandler dine personlige oplysninger til følgende formål: {purposes}.",imprint:{name:""},learnMore:"Læs mere",privacyPolicy:{name:"Datenschutzerklärung"}},decline:"Afvis",ok:"Ok",poweredBy:"Realiseret med Klaro!",purposeItem:{service:"",services:""},save:"Gem"},de:{acceptAll:"Alle akzeptieren",acceptSelected:"Ausgewählte akzeptieren",close:"Schließen",consentModal:{description:"Hier können Sie die Dienste, die wir auf dieser Website nutzen möchten, bewerten und anpassen. Sie haben das Sagen! Aktivieren oder deaktivieren Sie die Dienste, wie Sie es für richtig halten.",privacyPolicy:{name:"Datenschutzerklärung",text:"Um mehr zu erfahren, lesen Sie bitte unsere {privacyPolicy}."},title:"Dienste, die wir nutzen möchten"},consentNotice:{changeDescription:"Seit Ihrem letzten Besuch gab es Änderungen, bitte erneuern Sie Ihre Zustimmung.",title:"Cookie-Einstellungen",description:"Hallo! Könnten wir bitte einige zusätzliche Dienste für {purposes} aktivieren? Sie können Ihre Zustimmung später jederzeit ändern oder zurückziehen.",imprint:{name:"Impressum"},learnMore:"Lassen Sie mich wählen",privacyPolicy:{name:"Datenschutzerklärung"},testing:"Testmodus!"},contextualConsent:{acceptAlways:"Immer",acceptOnce:"Ja",description:"Möchten Sie von {title} bereitgestellte externe Inhalte laden?",descriptionEmptyStore:"Um diesem Dienst dauerhaft zustimmen zu können, müssen Sie {title} in den {link} zustimmen.",modalLinkText:"Cookie-Einstellungen"},decline:"Ich lehne ab",ok:"Das ist ok",poweredBy:"Realisiert mit Klaro!",privacyPolicy:{name:"Datenschutzerklärung",text:"Um mehr zu erfahren, lesen Sie bitte unsere {privacyPolicy}."},purposeItem:{service:"Dienst",services:"Dienste"},purposes:{advertising:{description:"Diese Dienste verarbeiten persönliche Informationen, um Ihnen personalisierte oder interessenbezogene Werbung zu zeigen.",title:"Werbung"},functional:{description:"Diese Dienste sind für die korrekte Funktion dieser Website unerlässlich. Sie können sie hier nicht deaktivieren, da der Dienst sonst nicht richtig funktionieren würde.\n",title:"Dienstbereitstellung"},marketing:{description:"Diese Dienste verarbeiten persönliche Daten, um Ihnen relevante Inhalte über Produkte, Dienstleistungen oder Themen zu zeigen, die Sie interessieren könnten.",title:"Marketing"},performance:{description:"Diese Dienste verarbeiten personenbezogene Daten, um den von dieser Website angebotenen Service zu optimieren.\n",title:"Optimierung der Leistung"}},save:"Speichern",service:{disableAll:{description:"Mit diesem Schalter können Sie alle Dienste aktivieren oder deaktivieren.",title:"Alle Dienste aktivieren oder deaktivieren"},optOut:{description:"Diese Dienste werden standardmäßig geladen (Sie können sich jedoch abmelden)",title:"(Opt-out)"},purpose:"Zweck",purposes:"Zwecke",required:{description:"Dieser Service ist immer erforderlich",title:"(immer erforderlich)"}}},el:{acceptAll:"",acceptAll_en:"Accept all",acceptSelected:"",acceptSelected_en:"Accept selected",service:{disableAll:{description:"Χρησιμοποίησε αυτό τον διακόπτη για να ενεργοποιήσεις/απενεργοποιήσεις όλες τις εφαρμογές.",title:"Για όλες τις εφαρμογές"},optOut:{description:"Είναι προκαθορισμένο να φορτώνεται, άλλα μπορεί να παραληφθεί",title:"(μη απαιτούμενο)"},purpose:"Σκοπός",purposes:"Σκοποί",required:{description:"Δεν γίνεται να λειτουργήσει σωστά η εφαρμογή χωρίς αυτό",title:"(απαιτούμενο)"}},close:"Κλείσιμο",consentModal:{description:"Εδώ μπορείς να δεις και να ρυθμίσεις τις πληροφορίες που συλλέγουμε σχετικά με εσένα.",privacyPolicy:{name:"Πολιτική Απορρήτου",text:"Για περισσότερες πληροφορίες, παρακαλώ διαβάστε την {privacyPolicy}."},title:"Πληροφορίες που συλλέγουμε"},consentNotice:{changeDescription:"Πραγματοποιήθηκαν αλλαγές μετά την τελευταία σας επίσκεψη παρακαλούμε ανανεώστε την συγκατάθεση σας.",description:"Συγκεντρώνουμε και επεξεργαζόμαστε τα προσωπικά δεδομένα σας για τους παρακάτω λόγους: {purposes}.",imprint:{name:"",name_en:"imprint"},learnMore:"Περισσότερα",privacyPolicy:{name:"Πολιτική Απορρήτου"}},decline:"Απόρριπτω",ok:"OK",poweredBy:"Υποστηρίζεται από το Klaro!",purposeItem:{service:"",services:""},save:"Αποθήκευση"},en:{acceptAll:"Accept all",acceptSelected:"Accept selected",close:"Close",consentModal:{description:"Here you can assess and customize the services that we'd like to use on this website. You're in charge! Enable or disable services as you see fit.",title:"Services we would like to use"},consentNotice:{changeDescription:"There were changes since your last visit, please renew your consent.",title:"Cookie Consent",description:"Hi! Could we please enable some additional services for {purposes}? You can always change or withdraw your consent later.",learnMore:"Let me choose",testing:"Testing mode!"},contextualConsent:{acceptAlways:"Always",acceptOnce:"Yes",description:"Do you want to load external content supplied by {title}?",descriptionEmptyStore:"To agree to this service permanently, you must accept {title} in the {link}.",modalLinkText:"Consent Manager"},decline:"I decline",ok:"That's ok",poweredBy:"Realized with Klaro!",privacyPolicy:{name:"privacy policy",text:"To learn more, please read our {privacyPolicy}."},purposeItem:{service:"service",services:"services"},purposes:{advertising:{description:"These services process personal information to show you personalized or interest-based advertisements.",title:"Advertising"},functional:{description:"These services are essential for the correct functioning of this website. You cannot disable them here as the service would not work correctly otherwise.\n",title:"Service Provision"},marketing:{description:"These services process personal information to show you relevant content about products, services or topics that you might be interested in.",title:"Marketing"},performance:{description:"These services process personal information to optimize the service that this website offers.\n",title:"Performance Optimization"}},save:"Save",service:{disableAll:{description:"Use this switch to enable or disable all services.",title:"Enable or disable all services"},optOut:{description:"This services is loaded by default (but you can opt out)",title:"(opt-out)"},purpose:"purpose",purposes:"purposes",required:{description:"This services is always required",title:"(always required)"}}},zh:{acceptAll:"照单全收",acceptSelected:"接受选择",close:"密切",consentModal:{description:"在这里，您可以评估和定制我们希望在本网站上使用的服务。您是负责人！您可以根据自己的需要启用或禁用服务。启用或禁用您认为合适的服务。",privacyPolicy:{name:"隐私政策",text:"要了解更多，请阅读我们的{privacyPolicy} 。"},title:"我们想使用的服务"},consentNotice:{changeDescription:"自上次访问后有变化，请更新您的同意。",description:"你好！我们可以为{purposes} 启用一些额外的服务吗？您可以随时更改或撤回您的同意。",imprint:{name:"印记"},learnMore:"让我来选",privacyPolicy:{name:"隐私政策"},testing:"测试模式！"},contextualConsent:{acceptAlways:"总是",acceptOnce:"是的，是的",description:"你想加载由{title} 提供的外部内容吗？"},decline:"我拒绝",ok:"没事的",poweredBy:"与Klaro一起实现!",privacyPolicy:{name:"隐私政策",text:"要了解更多，请阅读我们的{privacyPolicy} 。"},purposeItem:{service:"服务",services:"服务"},purposes:{advertising:{description:"这些服务处理个人信息，向您展示个性化或基于兴趣的广告。",title:"广告宣传"},functional:{description:"这些服务对于本网站的正常运行是必不可少的。您不能在这里禁用它们，否则服务将无法正常运行。\n",title:"服务提供"},marketing:{description:"这些服务会处理个人信息，向您展示您可能感兴趣的产品、服务或主题的相关内容。",title:"市场营销"},performance:{description:"这些服务处理个人信息是为了优化本网站提供的服务。\n",title:"性能优化"}},save:"挽救",service:{disableAll:{description:"使用此开关可启用或禁用所有服务。",title:"启用或停用所有服务"},optOut:{description:"这个服务是默认加载的(但你可以选择退出)",title:"(选择退出)"},purpose:"目的",purposes:"目的",required:{description:"这种服务是必须的",title:"(总是需要)"}}},pt:{acceptAll:"Aceitar todos",acceptSelected:"Aceitar selecionados",close:"Fechar",consentModal:{description:"Aqui você pode avaliar e personalizar os serviços que gostaríamos de usar neste website. Você está no comando! Habilite ou desabilite os serviços como julgar conveniente.",privacyPolicy:{name:"política de privacidade",text:"Para saber mais, por favor, leia nossa {privacyPolicy}."},title:"Serviços que gostaríamos de utilizar"},consentNotice:{changeDescription:"Houve mudanças desde sua última visita, queira renovar seu consentimento.",description:"Olá! Poderíamos, por favor, habilitar alguns serviços adicionais para {purposes}? Você pode sempre mudar ou retirar seu consentimento mais tarde.",imprint:{name:"imprimir"},learnMore:"Deixe-me escolher",privacyPolicy:{name:"política de privacidade"},testing:"Modo de teste!"},contextualConsent:{acceptAlways:"Sempre",acceptOnce:"Sim",description:"Você deseja carregar conteúdo externo fornecido por {title}?"},decline:"Recusar",ok:"Aceito.",poweredBy:"Realizado com Klaro!",privacyPolicy:{name:"política de privacidade",text:"Para saber mais, por favor, leia nossa {privacyPolicy}."},purposeItem:{service:"serviço",services:"serviços"},purposes:{advertising:{description:"Esses serviços processam informações pessoais para mostrar a você anúncios personalizados ou baseados em interesses.",title:"Publicidade"},functional:{description:"Esses serviços são essenciais para o correto funcionamento deste website. Você não pode desativá-los aqui, pois de outra forma o serviço não funcionaria corretamente.\n",title:"Prestação de serviços"},marketing:{description:"Esses serviços processam informações pessoais para mostrar a você conteúdo relevante sobre produtos, serviços ou tópicos que possam ser do seu interesse.",title:"Marketing"},performance:{description:"Esses serviços processam informações pessoais para otimizar o serviço que este website oferece.\n",title:"Otimização do desempenho"}},save:"Salvar",service:{disableAll:{description:"Use essa chave para habilitar ou desabilitar todos os serviços.",title:"Habilitar ou desabilitar todos os serviços"},optOut:{description:"Estes serviços são carregados por padrão (mas o você pode optar por não participar).",title:"(opt-out)"},purpose:"Objetivo",purposes:"Objetivos",required:{description:"Esses serviços são sempre necessários",title:"(sempre necessário)"}}},es:{acceptAll:"Aceptar todas",acceptSelected:"Aceptar seleccionadas",close:"Cerrar",consentModal:{description:"Aquí puede evaluar y personalizar los servicios que nos gustaría utilizar en este sitio web. ¡Usted decide! Habilite o deshabilite los servicios como considere oportuno.",privacyPolicy:{name:"política de privacidad",text:"Para saber más, por favor lea nuestra {privacyPolicy}."},title:"Servicios que nos gustaría utilizar"},consentNotice:{changeDescription:"Ha habido cambios en las cookies desde su última visita. Debe renovar su consentimiento.",description:"¡Hola! ¿Podríamos habilitar algunos servicios adicionales para {purposes}? Siempre podrá cambiar o retirar su consentimiento más tarde.",imprint:{name:"Imprimir"},learnMore:"Quiero elegir",privacyPolicy:{name:"política de privacidad"},testing:"¡Modo de prueba!"},contextualConsent:{acceptAlways:"Siempre",acceptOnce:"Sí",description:"¿Quieres cargar el contenido externo suministrado por {title}?"},decline:"Descartar todas",ok:"De acuerdo",poweredBy:"¡Realizado con Klaro!",privacyPolicy:{name:"política de privacidad",text:"Para saber más, por favor lea nuestra {privacyPolicy}."},purposeItem:{service:"servicio",services:"servicios"},purposes:{advertising:{description:"Estos servicios procesan información personal para mostrarle anuncios personalizados o basados en intereses.",title:"Publicidad"},functional:{description:"Estos servicios son esenciales para el correcto funcionamiento de este sitio web. No puede desactivarlos ya que la página no funcionaría correctamente.",title:"Prestación de servicios"},marketing:{description:"Estos servicios procesan información personal para mostrarle contenido relevante sobre productos, servicios o temas que puedan interesarle.",title:"Marketing"},performance:{description:"Estos servicios procesan información personal para optimizar el servicio que ofrece este sitio.",title:"Optimización del rendimiento"}},save:"Guardar",service:{disableAll:{description:"Utilice este interruptor para activar o desactivar todos los servicios.",title:"Activar o desactivar todos los servicios"},optOut:{description:"Este servicio está habilitado por defecto (pero puede optar por lo contrario)",title:"(desactivar)"},purpose:"Finalidad",purposes:"Finalidades",required:{description:"Este servicio es necesario siempre",title:"(siempre requerido)"}}},fi:{acceptAll:"",acceptAll_en:"Accept all",acceptSelected:"",acceptSelected_en:"Accept selected",service:{disableAll:{description:"Aktivoi kaikki päälle/pois.",title:"Valitse kaikki"},optOut:{description:"Ladataan oletuksena (mutta voit ottaa sen pois päältä)",title:"(ladataan oletuksena)"},purpose:"Käyttötarkoitus",purposes:"Käyttötarkoitukset",required:{description:"Sivusto vaatii tämän aina",title:"(vaaditaan)"}},close:"Sulje",consentModal:{description:"Voit tarkastella ja muokata sinusta keräämiämme tietoja.",privacyPolicy:{name:"tietosuojasivultamme",text:"Voit lukea lisätietoja {privacyPolicy}."},title:"Keräämämme tiedot"},consentNotice:{changeDescription:"Olemme tehneet muutoksia ehtoihin viime vierailusi jälkeen, tarkista ehdot.",description:"Keräämme ja käsittelemme henkilötietoja seuraaviin tarkoituksiin: {purposes}.",imprint:{name:"",name_en:"imprint"},learnMore:"Lue lisää",privacyPolicy:{name:"tietosuojasivultamme"}},decline:"Hylkää",ok:"Hyväksy",poweredBy:"Palvelun tarjoaa Klaro!",purposeItem:{service:"",services:""},save:"Tallenna"},fr:{acceptAll:"Accepter tout",acceptSelected:"Accepter sélectionné",close:"Fermer",consentModal:{description:"Vous pouvez ici évaluer et personnaliser les services que nous aimerions utiliser sur ce site. C'est vous qui décidez ! Activez ou désactivez les services comme bon vous semble.",privacyPolicy:{name:"politique de confidentialité",text:"Pour en savoir plus, veuillez lire notre {privacyPolicy}."},title:"Services que nous souhaitons utiliser"},consentNotice:{changeDescription:"Il y a eu des changements depuis votre dernière visite, veuillez renouveler votre consentement.",description:"Bonjour ! Pourrions-nous activer des services supplémentaires pour {purposes}? Vous pouvez toujours modifier ou retirer votre consentement plus tard.",imprint:{name:"mentions légales"},learnMore:"Laissez-moi choisir",privacyPolicy:{name:"politique de confidentialité"},testing:"Mode test !"},contextualConsent:{acceptAlways:"Toujours",acceptOnce:"Oui",description:"Vous souhaitez charger un contenu externe fourni par {title}?"},decline:"Je refuse",ok:"C'est bon.",poweredBy:"Réalisé avec Klaro !",privacyPolicy:{name:"politique de confidentialité",text:"Pour en savoir plus, veuillez lire notre {privacyPolicy}."},purposeItem:{service:"service",services:"services"},purposes:{advertising:{description:"Ces services traitent les informations personnelles pour vous présenter des publicités personnalisées ou basées sur des intérêts.",title:"Publicité"},functional:{description:"Ces services sont essentiels au bon fonctionnement de ce site. Vous ne pouvez pas les désactiver ici car le service ne fonctionnerait pas correctement autrement.\n",title:"Prestation de services"},marketing:{description:"Ces services traitent les informations personnelles afin de vous présenter un contenu pertinent sur les produits, les services ou les sujets qui pourraient vous intéresser.",title:"Marketing"},performance:{description:"Ces services traitent les informations personnelles afin d'optimiser le service que ce site Web offre.\n",title:"Optimisation de la performance"}},save:"Enregistrer",service:{disableAll:{description:"Utilisez ce commutateur pour activer ou désactiver tous les services.",title:"Activer ou désactiver tous les services"},optOut:{description:"Ce service est chargé par défaut (mais vous pouvez le désactiver)",title:"(opt-out)"},purpose:"Objet",purposes:"Fins",required:{description:"Ce service est toujours nécessaire",title:"(toujours requis)"}}},gl:{acceptAll:"Aceptar todas",acceptSelected:"Aceptar seleccionadas",close:"Pechar",consentModal:{description:"Aquí pode avaliar e personalizar os servizos que nos gustaría utilizar neste sitio web. ¡Vostede decide! Habilite ou deshabilite os servicios como lle conveña.",privacyPolicy:{name:"política de privacidade",text:"Para saber máis, por favor lea a nosa {privacyPolicy}."},title:"Servizos que nos gustaría utilizar"},consentNotice:{changeDescription:"Houbo cambios nas cookies dende a súa última visita. Debe renovar o seu consentimento.",description:"¡Ola! ¿Poderíamos habilitar algúns servizos adicionais para {purposes}? Sempre poderá cambiar ou retirar o séu consentimento máis tarde.",imprint:{name:"Imprimir"},learnMore:"Quero elixir",privacyPolicy:{name:"política de privacidade"},testing:"¡Modo de proba!"},decline:"Descartar todas",ok:"De acordo",poweredBy:"¡Realizado con Klaro!",privacyPolicy:{name:"política de privacidade",text:"Para saber máis, por favor lea a nosa {privacyPolicy}."},purposeItem:{service:"servizo",services:"servizos"},purposes:{advertising:{description:"Estes servizos procesan información persoal para mostrarlle anuncios personalizados ou basados en intereses.",title:"Publicidade"},functional:{description:"Estes servizos son esenciais para o correcto funcionamiento deste sitio web. Non pode desactivalos xa que a páxina non funcionaría correctamente.",title:"Prestación de servizos"},marketing:{description:"Estes servizos procesan información persoal para mostrarlle contido relevante sobre produtos, servizos ou temas que poidan interesarlle.",title:"Marketing"},performance:{description:"Estes servizos procesan información persoal para optimizar o servizo que ofrece este sitio.",title:"Optimización do rendimento"}},save:"Gardar",service:{disableAll:{description:"Utilice este interruptor para activar ou desactivar todos os servizos.",title:"Activar ou desactivar todos os servizos"},optOut:{description:"Este servizo está habilitado por defecto (pero pode optar polo contrario)",title:"(desactivar)"},purpose:"Finalidade",purposes:"Finalidades",required:{description:"Este servizo é necesario sempre",title:"(sempre requirido)"}}},hu:{acceptAll:"Mind elfogad",acceptAll_en:"Accept all",acceptSelected:"Kiválasztottat elfogad",acceptSelected_en:"Accept selected",service:{disableAll:{description:"Használja ezt a kapcsolót az összes alkalmazás engedélyezéséhez/letiltásához.",title:"Összes app átkapcsolása"},optOut:{description:"Ez az alkalmazás alapértelmezés szerint betöltött (de ki lehet kapcsolni)",title:"(leiratkozás)"},purpose:"Cél",purposes:"Célok",required:{description:"Ez az alkalmazás mindig szükséges",title:"(mindig szükséges)"}},close:"Elvet",consentModal:{description:"Itt láthatja és testreszabhatja az önről gyűjtött információkat.",privacyPolicy:{name:"adatvédelmi irányelveinket",text:"További információért kérjük, olvassa el az {privacyPolicy}."},title:"Információk, amiket gyűjtünk"},consentNotice:{changeDescription:"Az utolsó látogatás óta változások történtek, kérjük, frissítse a hozzájárulását.",description:"Személyes adatait összegyűjtjük és feldolgozzuk az alábbi célokra: {purposes}.",imprint:{name:"impresszum",name_en:"imprint"},learnMore:"Tudjon meg többet",privacyPolicy:{name:"adatvédelmi irányelveinket"}},contextualConsent:{acceptAlways:"Mindig",acceptOnce:"Igen",description:"Be akarod tölteni a {title} által szolgáltatott külső tartalmakat?"},decline:"Elutasít",ok:"Elfogad",poweredBy:"Powered by Klaro!",purposeItem:{service:"",services:""},save:"Mentés"},hr:{acceptAll:"",acceptAll_en:"Prihvati sve",acceptSelected:"",acceptSelected_en:"Prihvati odabrane",service:{disableAll:{description:"Koristite ovaj prekidač da omogućite/onemogućite sve aplikacije odjednom.",title:"Izmeijeni sve"},optOut:{description:"Ova aplikacija je učitana automatski (ali je možete onemogućiti)",title:"(onemogućite)"},purpose:"Svrha",purposes:"Svrhe",required:{description:"Ova aplikacija je uvijek obavezna",title:"(obavezna)"}},close:"Zatvori",consentModal:{description:"Ovdje možete vidjeti i podesiti informacije koje prikupljamo o Vama.",privacyPolicy:{name:"pravila privatnosti",text:"Za više informacije pročitajte naša {privacyPolicy}."},title:"Informacije koje prikupljamo"},consentNotice:{changeDescription:"Došlo je do promjena od Vaše posljednjeg posjećivanja web stranice, molimo Vas da ažurirate svoja odobrenja.",description:"Mi prikupljamo i procesiramo Vaše osobne podatke radi slijedećeg: {purposes}.",imprint:{name:"",name_en:"imprint"},learnMore:"Saznajte više",privacyPolicy:{name:"pravila privatnosti"}},decline:"Odbij",ok:"U redu",poweredBy:"Pokreće Klaro!",purposeItem:{service:"",services:""},save:"Spremi"},it:{acceptAll:"Accettare tutti",acceptSelected:"Accettare selezionato",close:"Chiudi",consentModal:{description:"Qui può valutare e personalizzare i servizi che vorremmo utilizzare su questo sito web. È lei il responsabile! Abilitare o disabilitare i servizi come meglio crede.",privacyPolicy:{name:"informativa sulla privacy",text:"Per saperne di più, legga la nostra {privacyPolicy}."},title:"Servizi che desideriamo utilizzare"},consentNotice:{changeDescription:"Ci sono stati dei cambiamenti rispetto alla sua ultima visita, la preghiamo di rinnovare il suo consenso.",description:"Salve, possiamo attivare alcuni servizi aggiuntivi per {purposes}? Può sempre modificare o ritirare il suo consenso in un secondo momento.",imprint:{name:"impronta"},learnMore:"Lasciatemi scegliere",privacyPolicy:{name:"informativa sulla privacy"},testing:"Modalità di test!"},contextualConsent:{acceptAlways:"Sempre",acceptOnce:"Sì",description:"Vuole caricare contenuti esterni forniti da {title}?"},decline:"Rifiuto",ok:"Va bene così",poweredBy:"Realizzato con Klaro!",privacyPolicy:{name:"informativa sulla privacy",text:"Per saperne di più, legga la nostra {privacyPolicy}."},purposeItem:{service:"servizio",services:"servizi"},purposes:{advertising:{description:"Questi servizi elaborano le informazioni personali per mostrarle annunci pubblicitari personalizzati o basati su interessi.",title:"Pubblicità"},functional:{description:"Questi servizi sono essenziali per il corretto funzionamento di questo sito web. Non può disattivarli qui perché altrimenti il servizio non funzionerebbe correttamente.\n",title:"Fornitura di servizi"},marketing:{description:"Questi servizi elaborano le informazioni personali per mostrarle contenuti rilevanti su prodotti, servizi o argomenti che potrebbero interessarla.",title:"Marketing"},performance:{description:"Questi servizi elaborano le informazioni personali per ottimizzare il servizio offerto da questo sito web.\n",title:"Ottimizzazione delle prestazioni"}},save:"Salva",service:{disableAll:{description:"Utilizzi questo interruttore per attivare o disattivare tutti i servizi.",title:"Attivare o disattivare tutti i servizi"},optOut:{description:"Questo servizio è caricato di default (ma è possibile scegliere di non usufruirne)",title:"(opt-out)"},purpose:"Scopo dell",purposes:"Finalità",required:{description:"Questo servizio è sempre richiesto",title:"(sempre richiesto)"}}},nl:{acceptAll:"Accepteer alle",acceptSelected:"Geselecteerde",close:"Sluit",consentModal:{description:"Hier kunt u de diensten die wij op deze website willen gebruiken beoordelen en aanpassen. U heeft de leiding! Schakel de diensten naar eigen inzicht in of uit.",privacyPolicy:{name:"privacybeleid",text:"Voor meer informatie kunt u ons {privacyPolicy} lezen."},title:"Diensten die we graag willen gebruiken"},consentNotice:{changeDescription:"Er waren veranderingen sinds uw laatste bezoek, gelieve uw toestemming te hernieuwen.",description:"Hallo, kunnen wij u een aantal extra diensten aanbieden voor {purposes}? U kunt uw toestemming later altijd nog wijzigen of intrekken.",imprint:{name:"impressum"},learnMore:"Laat me kiezen",privacyPolicy:{name:"privacybeleid"},testing:"Testmodus!"},contextualConsent:{acceptAlways:"Altijd",acceptOnce:"Ja",description:"Wilt u externe content laden die door {title} wordt aangeleverd ?"},decline:"Ik weiger",ok:"Dat is oké",poweredBy:"Gerealiseerd met Klaro!",privacyPolicy:{name:"privacybeleid",text:"Voor meer informatie kunt u ons {privacyPolicy} lezen."},purposeItem:{service:"service",services:"diensten"},purposes:{advertising:{description:"Deze diensten verwerken persoonlijke informatie om u gepersonaliseerde of op interesse gebaseerde advertenties te tonen.",title:"Reclame"},functional:{description:"Deze diensten zijn essentieel voor het correct functioneren van deze website. U kunt ze hier niet uitschakelen omdat de dienst anders niet correct zou werken.\n",title:"Dienstverlening"},marketing:{description:"Deze diensten verwerken persoonlijke informatie om u relevante inhoud te tonen over producten, diensten of onderwerpen waarin u geïnteresseerd zou kunnen zijn.",title:"Marketing"},performance:{description:"Deze diensten verwerken persoonlijke informatie om de service die deze website biedt te optimaliseren.\n",title:"Optimalisatie van de prestaties"}},save:"Opslaan",service:{disableAll:{description:"Gebruik deze schakelaar om alle diensten in of uit te schakelen.",title:"Alle diensten in- of uitschakelen"},optOut:{description:"Deze diensten worden standaard geladen (maar u kunt zich afmelden)",title:"(opt-out)"},purpose:"Verwerkingsdoel",purposes:"Verwerkingsdoeleinden",required:{description:"Deze diensten zijn altijd nodig",title:"(altijd nodig)"}}},no:{acceptAll:"Godtar alle",acceptSelected:"Godtar valgt",service:{disableAll:{description:"Bruk denne for å skru av/på alle apper.",title:"Bytt alle apper"},optOut:{description:"Denne appen er lastet som standard (men du kan skru det av)",title:"(opt-out)"},purpose:"Årsak",purposes:"Årsaker",required:{description:"Denne applikasjonen er alltid påkrevd",title:"(alltid påkrevd)"}},close:"",close_en:"Close",consentModal:{description:"Her kan du se og velge hvilken informasjon vi samler inn om deg.",privacyPolicy:{name:"personvernerklæring",text:"For å lære mer, vennligst les vår {privacyPolicy}."},title:"Informasjon vi samler inn"},consentNotice:{changeDescription:"Det har skjedd endringer siden ditt siste besøk, vennligst oppdater ditt samtykke.",description:"Vi samler inn og prosesserer din personlige informasjon av følgende årsaker: {purposes}.",imprint:{name:"",name_en:"imprint"},learnMore:"Lær mer",privacyPolicy:{name:"personvernerklæring"}},decline:"Avslå",ok:"OK",poweredBy:"Laget med Klaro!",purposeItem:{service:"",services:""},save:"Opslaan"},oc:{acceptAll:"Tot acceptar",acceptSelected:"Acceptar çò seleccionat",close:"Tampar",consentModal:{description:"Aquí podètz mesurar e personalizar los servicis que volriam utilizar sus aqueste site web. Avètz lo darrièr mot ! Activatz o desactivatz segon vòstra causida.",title:"Servicis que volriam utilizar"},consentNotice:{changeDescription:"I aguèt de modificacions dempuèi vòstra darrièra visita, mercés de repassar vòstre consentiment.",description:"Adieu ! Poiriam activar mai de servici per {purposes} ? Podètz totjorn modificar o tirar vòstre consentiment mai tard.",learnMore:"Me daissar causir",testing:"Mòde tèst !"},contextualConsent:{acceptAlways:"Totjorn",acceptOnce:"Òc",description:"Volètz cargar de contenguts extèrn provesits per {title} ?"},decline:"Refusi",ok:"Es bon",poweredBy:"Realizat amb Klaro !",privacyPolicy:{name:"politica de confidencialitat",text:"Per ne saber mai, vejatz nòstra {privacyPolicy}."},purposeItem:{service:"servici",services:"servicis"},purposes:{advertising:{description:"Aquestes servicis tractan d’informacions personalas per vos mostrar de reclamas personalizadas o basadas suls interèsses.",title:"Reclama"},functional:{description:"Aquestes servicis son essencials pel foncionament corrèct d’aqueste site web. Los podètz pas desactivar aquí pr’amor que lo servici foncionariá pas coma cal autrament.\n",title:"Servici de provision"},marketing:{description:"Aquestes servicis tractan d’informacions personalas per vos mostrar de contenguts a prepaus de produits, de servicis o tèmas que poirián vos interessar.",title:"Marketing"},performance:{description:"Aquestes servicis tractan d’informacions per optimizar lo servici qu’aqueste site web prepausa.\n",title:"Optimizacion de las performanças"}},save:"Salvar",service:{disableAll:{description:"Utilizatz aqueste alternator per activar o desactivar totes los servicis.",title:"Activar o desactivar totes los servicis"},optOut:{description:"Aqueste servici es cargar per defaut (mas lo podètz desactivar)",title:"(opt-out)"},purpose:"finalitat",purposes:"finalitat",required:{description:"Aqueste servici es totjorn requesit",title:"(totjorn requesit)"}}},ro:{acceptAll:"",acceptAll_en:"Accept all",acceptSelected:"",acceptSelected_en:"Accept selected",service:{disableAll:{description:"Utilizați acest switch pentru a activa/dezactiva toate aplicațiile.",title:"Comutați între toate aplicațiile"},optOut:{description:"Această aplicație este încărcată în mod implicit (dar puteți renunța)",title:"(opt-out)"},purpose:"Scop",purposes:"Scopuri",required:{description:"Această aplicație este întotdeauna necesară",title:"(întotdeauna necesar)"}},close:"",close_en:"Close",consentModal:{description:"Aici puteți vedea și personaliza informațiile pe care le colectăm despre dvs.",privacyPolicy:{name:"politica privacy",text:"Pentru a afla mai multe, vă rugăm să citiți {privacyPolicy}."},title:"Informațiile pe care le colectăm"},consentNotice:{changeDescription:"Au existat modificări de la ultima vizită, vă rugăm să actualizați consimțământul.",description:"Colectăm și procesăm informațiile dvs. personale în următoarele scopuri: {purposes}.",imprint:{name:"",name_en:"imprint"},learnMore:"Află mai multe",privacyPolicy:{name:"politica privacy"}},decline:"Renunță",ok:"OK",poweredBy:"Realizat de Klaro!",purposeItem:{service:"",services:""},save:"Salvează"},sr:{acceptAll:"",acceptAll_en:"Accept all",acceptSelected:"",acceptSelected_en:"Accept selected",service:{disableAll:{description:"Koristite ovaj prekidač da omogućite/onesposobite sve aplikacije odjednom.",title:"Izmeni sve"},optOut:{description:"Ova aplikacija je učitana automatski (ali je možete onesposobiti)",title:"(onesposobite)"},purpose:"Svrha",purposes:"Svrhe",required:{description:"Ova aplikacija je uvek neophodna",title:"(neophodna)"}},close:"Zatvori",consentModal:{description:"Ovde možete videti i podesiti informacije koje prikupljamo o Vama.",privacyPolicy:{name:"politiku privatnosti",text:"Za više informacije pročitajte našu {privacyPolicy}."},title:"Informacije koje prikupljamo"},consentNotice:{changeDescription:"Došlo je do promena od Vaše poslednje posete, molimo Vas da ažurirate svoja odobrenja.",description:"Mi prikupljamo i procesiramo Vaše lične podatke radi sledećeg: {purposes}.",imprint:{name:"",name_en:"imprint"},learnMore:"Saznajte više",privacyPolicy:{name:"politiku privatnosti"}},decline:"Odbij",ok:"U redu",poweredBy:"Pokreće Klaro!",purposeItem:{service:"",services:""},save:"Sačuvaj"},sr_cyrl:{consentModal:{title:"Информације које прикупљамо",description:"Овде можете видет и подесити информације које прикупљамо о Вама.\n",privacyPolicy:{name:"политику приватности",text:"За више информација прочитајте нашу {privacyPolicy}.\n"}},consentNotice:{changeDescription:"Дошло је до промена од Ваше последнје посете, молимо Вас да ажурирате своја одобрења.",description:"Ми прикупљамо и процесирамо Ваше личне податке ради следећег: {purposes}.\n",learnMore:"Сазнајте више",privacyPolicy:{name:"политику приватности"}},ok:"У реду",save:"Сачувај",decline:"Одбиј",close:"Затвори",service:{disableAll:{title:"Измени све",description:"Користите овај прекидач да омогућите/онеспособите све апликације одједном."},optOut:{title:"(онеспособите)",description:"Ова апликација је учитана аутоматски (али је можете онеспособити)"},required:{title:"(неопходна)",description:"Ова апликација је увек неопходна."},purposes:"Сврхе",purpose:"Сврха"},poweredBy:"Покреће Кларо!"},sv:{acceptAll:"Acceptera alla",acceptSelected:"Acceptera markerat",service:{disableAll:{description:"Använd detta reglage för att aktivera/avaktivera samtliga appar.",title:"Ändra för alla appar"},optOut:{description:"Den här appen laddas som standardinställning (men du kan avaktivera den)",title:"(Avaktivera)"},purpose:"Syfte",purposes:"Syften",required:{description:"Den här applikationen krävs alltid",title:"(Krävs alltid)"}},close:"Stäng",consentModal:{description:"Här kan du se och anpassa vilken information vi samlar om dig.",privacyPolicy:{name:"Integritetspolicy",text:"För att veta mer, läs vår {privacyPolicy}."},title:"Information som vi samlar"},consentNotice:{changeDescription:"Det har skett förändringar sedan ditt senaste besök, var god uppdatera ditt medgivande.",description:"Vi samlar och bearbetar din personliga data i följande syften: {purposes}.",imprint:{name:"",name_en:"imprint"},learnMore:"Läs mer",privacyPolicy:{name:"Integritetspolicy"}},decline:"Avböj",ok:"OK",poweredBy:"Körs på Klaro!",purposeItem:{service:"",services:""},save:"Spara"},tr:{acceptAll:"",acceptAll_en:"Accept all",acceptSelected:"",acceptSelected_en:"Accept selected",service:{disableAll:{description:"Toplu açma/kapama için bu düğmeyi kullanabilirsin.",title:"Tüm uygulamaları aç/kapat"},optOut:{description:"Bu uygulama varsayılanda yüklendi (ancak iptal edebilirsin)",title:"(isteğe bağlı)"},purpose:"Amaç",purposes:"Amaçlar",required:{description:"Bu uygulama her zaman gerekli",title:"(her zaman gerekli)"}},close:"Kapat",consentModal:{description:"Hakkınızda topladığımız bilgileri burada görebilir ve özelleştirebilirsiniz.",privacyPolicy:{name:"Gizlilik Politikası",text:"Daha fazlası için lütfen {privacyPolicy} sayfamızı okuyun."},title:"Sakladığımız bilgiler"},consentNotice:{changeDescription:"Son ziyaretinizden bu yana değişiklikler oldu, lütfen seçiminizi güncelleyin.",description:"Kişisel bilgilerinizi aşağıdaki amaçlarla saklıyor ve işliyoruz: {purposes}.",imprint:{name:"",name_en:"imprint"},learnMore:"Daha fazla bilgi",privacyPolicy:{name:"Gizlilik Politikası"}},decline:"Reddet",ok:"Tamam",poweredBy:"Klaro tarafından geliştirildi!",purposeItem:{service:"",services:""},save:"Kaydet"},pl:{acceptAll:"Zaakceptuj wszystkie",acceptSelected:"Zaakceptuj wybrane",close:"Zamknij",consentModal:{description:"Tutaj mogą Państwo ocenić i dostosować usługi, które chcielibyśmy wykorzystać na tej stronie. Włączaj lub wyłączaj usługi według własnego uznania.",privacyPolicy:{name:"polityką prywatności",text:"Aby dowiedzieć się więcej, prosimy o zapoznanie się z naszą {privacyPolicy}."},title:"Usługi, z których chcielibyśmy skorzystać"},consentNotice:{changeDescription:"Od Twojej ostatniej wizyty nastąpiły zmiany, prosimy o odnowienie zgody.",description:"Czy możemy włączyć dodatkowe usługi dla {purposes}? W każdej chwili mogą Państwo później zmienić lub wycofać swoją zgodę.",imprint:{name:"Imprint"},learnMore:"Pozwól mi wybrać",privacyPolicy:{name:"polityka prywatności"},testing:"Tryb testowy!"},contextualConsent:{acceptAlways:"Zawsze",acceptOnce:"Tak",description:"Czy chcą Państwo załadować treści zewnętrzne dostarczane przez {title}?"},decline:"Odmawiam",ok:"Ok",poweredBy:"Technologia dostarczona przez Klaro",privacyPolicy:{name:"polityka prywatności",text:"Aby dowiedzieć się więcej, prosimy o zapoznanie się z naszą {privacyPolicy}."},purposeItem:{service:"usługa",services:"usługi"},purposes:{advertising:{description:"Usługi te przetwarzają dane osobowe w celu pokazania Państwu spersonalizowanych lub opartych na zainteresowaniach reklam.",title:"Reklama"},functional:{description:"Usługi te są niezbędne do prawidłowego funkcjonowania niniejszej strony internetowej. Nie mogą Państwo ich tutaj wyłączyć, ponieważ w przeciwnym razie strona nie działałaby prawidłowo.\n",title:"Świadczenie usług"},marketing:{description:"Usługi te przetwarzają dane osobowe w celu pokazania Państwu istotnych treści dotyczących produktów, usług lub tematów, którymi mogą być Państwo zainteresowani.",title:"Marketing"},performance:{description:"Usługi te przetwarzają dane osobowe w celu optymalizacji usług oferowanych przez tę stronę.\n",title:"Optymalizacja wydajności"}},save:"Zapisz",service:{disableAll:{description:"Za pomocą tego przełącznika można włączać lub wyłączać wszystkie usługi.",title:"Włącz lub wyłącz wszystkie usługi"},optOut:{description:"Ta usługa jest domyślnie załadowana (ale mogą Państwo z niej zrezygnować)",title:"(opt-out)"},purpose:"Cel",purposes:"Cele",required:{description:"Usługi te są zawsze wymagane",title:"(zawsze wymagane)"}}},ru:{acceptAll:"Принять всё",acceptSelected:"Принять выбранные",service:{disableAll:{description:"Используйте этот переключатель, чтобы включить/отключить все приложения.",title:"Переключить все приложения"},optOut:{description:"Это приложение включено по умолчанию (но вы можете отказаться)",title:"(отказаться)"},purpose:"Намерение",purposes:"Намерения",required:{description:"Это обязательное приложение",title:"(всегда обязательный)"}},close:"Закрыть",consentModal:{description:"Здесь вы можете просмотреть и настроить, какую информацию о вас мы храним.",privacyPolicy:{name:"Соглашение",text:"Чтобы узнать больше, пожалуйста, прочитайте наше {privacyPolicy}."},title:"Информация, которую мы сохраняем"},consentNotice:{changeDescription:"Со времени вашего последнего визита произошли изменения, обновите своё согласие.",description:"Мы собираем и обрабатываем вашу личную информацию для следующих целей: {purposes}.",imprint:{name:"",name_en:"imprint"},learnMore:"Настроить",privacyPolicy:{name:"политика конфиденциальности"}},decline:"Отклонить",ok:"Принять",poweredBy:"Работает на Кларо!",purposeItem:{service:"",services:""},save:"Сохранить"}});Br(dn,Cn),jn()})(),n})()));