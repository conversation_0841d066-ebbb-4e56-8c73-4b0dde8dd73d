{"version": 2, "buildCommand": "next build", "outputDirectory": ".next", "framework": "nextjs", "regions": ["bom1"], "env": {"DATABASE_URL": "postgresql://personal-portfolio_owner:<EMAIL>/personal-portfolio?sslmode=require"}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Access-Control-Allow-Origin", "value": "https://vishal.biyani.xyz"}, {"key": "Access-Control-Allow-Methods", "value": "GET,OPTIONS,PATCH,DELETE,POST,PUT"}, {"key": "Access-Control-Allow-Headers", "value": "X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version"}]}], "functions": {"api/copilotkit/**/*": {"maxDuration": 60}}}