/** @type {import("next").NextConfig} */
module.exports = {
  // Remove the output: "standalone" to ensure API routes work
  async headers() {
    return [
      {
        // Apply these headers to all routes
        source: '/:path*',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            // Allow both localhost and your GitHub Pages domain
            value: process.env.NODE_ENV === 'production'
              ? 'https://vishal.biyani.xyz'
              : 'http://localhost:3775',
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET, POST, PUT, DELETE, OPTIONS',
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version',
          },
          {
            key: 'Access-Control-Allow-Credentials',
            value: 'true',
          },
        ],
      },
    ];
  },
}
