export const resumeData = [
    {
      section: "Experience",
      content: "Senior Analyst at CoreCard Software. Responsible for delivery of payment processing solutions, system integration, and client support. Led multiple successful implementations of credit card processing systems for financial institutions."
    },
    {
      section: "Skills",
      content: "React, JavaScript, Python, SQL, Project Management, System Integration, Payment Processing, API Development, Database Management, Agile Methodologies, Team Leadership, Client Relations"
    },
    {
      section: "Education",
      content: "Bachelor's Degree in Computer Science from XYZ University. Specialized in Software Engineering and Database Systems. Graduated with honors and completed multiple projects in web development and system design."
    },
    {
      section: "Certifications",
      content: "AWS Certified Solutions Architect, Certified Scrum Master (CSM), Oracle Certified Professional (OCP), Payment Card Industry Professional (PCIP)"
    },
    {
      section: "Projects",
      content: "Developed and implemented a scalable payment processing system, created a real-time transaction monitoring dashboard, built a customer portal with React and Node.js, designed and deployed microservices architecture"
    },
    {
      section: "Technical Expertise",
      content: "Full-stack development, cloud architecture, database design, API development, system integration, payment processing systems, security protocols, performance optimization, automated testing"
    },
    {
      section: "Leadership",
      content: "Led cross-functional teams in successful project implementations, mentored junior developers, established best practices for code quality and testing, managed client relationships and expectations"
    },
    {
      section: "Achievements",
      content: "Successfully implemented payment processing systems for 10+ financial institutions, reduced system downtime by 40%, improved transaction processing speed by 25%, received multiple client commendations"
    }
  ];
  