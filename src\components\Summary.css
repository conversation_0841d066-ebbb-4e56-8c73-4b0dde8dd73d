/* Summary section styles */
.summary-section {
  padding: 4rem 0;
  background-color: var(--background-color);
}

.summary-card {
  transition: all 0.3s ease-in-out;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.summary-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.summary-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-right: 1rem;
}

.summary-title {
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--primary-color);
}

.summary-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.summary-list-item {
  position: relative;
  padding-left: 1.5rem;
  margin-bottom: 0.75rem;
  line-height: 1.5;
}

.summary-list-item::before {
  content: "•";
  position: absolute;
  left: 0;
  color: var(--primary-color);
  font-weight: bold;
}

/* Dark mode adjustments */
[data-theme="dark"] .summary-card {
  background-color: var(--background-color);
  border: 1px solid var(--border-color);
}

[data-theme="dark"] .summary-list-item::before {
  color: var(--primary-light-color);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .summary-section {
    padding: 2rem 0;
  }

  .summary-card {
    margin-bottom: 1.5rem;
  }
}
