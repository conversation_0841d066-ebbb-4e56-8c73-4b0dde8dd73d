.themeToggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: transparent;
  border: none;
  cursor: pointer;
  color: var(--ifm-navbar-link-color);
  transition: all 0.3s ease;
  padding: 0;
  margin-left: 8px;
}

.themeToggle:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.themeToggle:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--ifm-color-primary);
}

/* Animation for the toggle */
.themeToggle svg {
  transition: transform 0.3s ease;
}

.themeToggle:hover svg {
  transform: rotate(30deg);
}
