# Production environment variables
# Auth.js server URL (deployed on Vercel)
REACT_APP_AUTH_SERVER_URL=https://my-next-auth-app-ten.vercel.app

# Client URL (your React app on GitHub Pages)
REACT_APP_CLIENT_URL=https://vishal.biyani.xyz

# Legacy URLs (for backward compatibility)
REACT_APP_AUTH_URL=https://my-next-auth-app-ten.vercel.app/api/auth
REACT_APP_REDIRECT_URI=https://my-next-auth-app-ten.vercel.app/api/auth/callback/google
REACT_APP_TOKEN_PROXY_URL=https://my-next-auth-app-ten.vercel.app/api/auth

# OAuth Client IDs for production (vishal.biyani.xyz)
# These should be the production client IDs registered for your domain
REACT_APP_GITHUB_CLIENT_ID=Ov23lin0vaP7eE5oNyKj
REACT_APP_GOOGLE_CLIENT_ID=790989392859-j9eei35mhqt451hmg3nbsr2nddosbhk0.apps.googleusercontent.com
