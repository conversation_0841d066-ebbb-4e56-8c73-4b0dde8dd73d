---
sidebar_position: 1
---

# Portfolio Documentation

Welcome to the documentation for the Portfolio project. This documentation provides detailed information about the architecture, libraries, and components used in the project.

## Overview

The Portfolio project is a personal website built with React and Material-UI. It showcases skills, experience, projects, and blog posts. The site is designed to be responsive, accessible, and user-friendly.

## Features

- **Resume Sections**: Display professional information including skills, experience, education, and certifications
- **Blog**: Create, view, edit, and delete blog posts with a rich text editor
- **Knowledge Base**: Share domain knowledge and glossary terms
- **Authentication**: Secure access to protected routes with Auth.js
- **Responsive Design**: Optimized for all screen sizes

## Technology Stack

- **Frontend**: React, Material-UI, Slate.js
- **Authentication**: Auth.js
- **Deployment**: GitHub Pages
- **Documentation**: Docusaurus

## Getting Started

To get started with the documentation, navigate through the sidebar to explore different sections:

- **Architecture**: Learn about the overall structure and design patterns
- **Libraries**: Explore the key libraries and frameworks used
- **SBOM**: Review the Software Bill of Materials and dependencies

## Contributing

Contributions to the documentation are welcome. Please follow these steps:

1. Fork the repository
2. Create a new branch
3. Make your changes
4. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
