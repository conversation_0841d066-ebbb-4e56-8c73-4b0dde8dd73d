/**
 * CSS files with the .module.css suffix will be treated as CSS modules
 * and scoped locally.
 */

.heroBanner {
  padding: 6rem 0;
  text-align: center;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, var(--ifm-color-primary-darkest), var(--ifm-color-primary-dark));
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
}

.heroContent {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 2rem;
}

.heroTitle {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  font-weight: 700;
}

.heroSubtitle {
  font-size: 1.5rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

@media screen and (max-width: 996px) {
  .heroBanner {
    padding: 3rem 1rem;
    min-height: 50vh;
  }

  .heroTitle {
    font-size: 2.5rem;
  }

  .heroSubtitle {
    font-size: 1.2rem;
  }
}

.buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
}

.primaryButton, .secondaryButton {
  padding: 0.8rem 1.5rem;
  border-radius: 4px;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  text-decoration: none;
}

.primaryButton {
  background-color: white;
  color: var(--ifm-color-primary-dark);
  border: 2px solid white;
}

.primaryButton:hover {
  background-color: transparent;
  color: white;
  text-decoration: none;
}

.secondaryButton {
  background-color: transparent;
  color: white;
  border: 2px solid white;
}

.secondaryButton:hover {
  background-color: rgba(255, 255, 255, 0.1);
  text-decoration: none;
}

/* Features section */
.features {
  padding: 4rem 2rem;
  background-color: var(--ifm-background-color);
}

.featuresContainer {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.featureCard {
  flex: 1;
  min-width: 280px;
  max-width: 350px;
  padding: 2rem;
  border-radius: 8px;
  background-color: var(--ifm-card-background-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  text-align: center;
}

.featureCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.featureIcon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.featureTitle {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.featureDescription {
  color: var(--ifm-color-emphasis-700);
  margin-bottom: 1.5rem;
}

.featureLink {
  color: var(--ifm-color-primary);
  font-weight: 600;
  text-decoration: none;
}

.featureLink:hover {
  text-decoration: underline;
}
