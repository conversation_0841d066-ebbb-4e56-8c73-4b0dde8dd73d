name: Deploy to Production
on:
  push:
    branches:
      - main
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20.10.0"

      # Portfolio Build and Setup
      - name: Install portfolio dependencies
        run: npm ci

      - name: Build the portfolio project
        run: npm run build
        env:
          REACT_APP_GITHUB_CLIENT_ID: ${{ vars.REACT_APP_GITHUB_CLIENT_ID }}
          REACT_APP_REDIRECT_URI: ${{ vars.REACT_APP_REDIRECT_URI }}
          REACT_APP_TOKEN_PROXY_URL: ${{ vars.REACT_APP_TOKEN_PROXY_URL }}

      # Copy 404.html to build folder for main site
      - name: Copy 404.html for main site
        run: |
          if [ -f "public/404.html" ]; then
            echo "Copying 404.html from public directory"
            cp public/404.html build/
          else
            echo "Creating 404.html in build directory"
            cat > build/404.html <<"EOF"
            <!DOCTYPE html>
            <html>
              <head>
                <meta charset="utf-8" />
                <script type="text/javascript">
                  (function () {
                    const search = window.location.search;
                    const params = new URLSearchParams(search);
                    const redirect = params.get("redirect") || "/";
                    window.location.replace(redirect);
                  })();
                </script>
              </head>
              <body>
                <p>Redirecting... If you are not redirected automatically, <a href="/">click here</a>.</p>
              </body>
            </html>
          EOF
          fi

      # Create docs directory and basic index.html
      - name: Create basic docs index
        run: |
          mkdir -p build/docs
          cat > build/docs/index.html <<"EOF"
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <meta http-equiv="refresh" content="0;url=/docs/">
            <title>Portfolio Documentation</title>
            <style>
              body {
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 20px;
                text-align: center;
              }
              .loading {
                display: inline-block;
                margin-top: 20px;
              }
              .loading:after {
                content: " .";
                animation: dots 1.5s steps(5, end) infinite;
              }
              @keyframes dots {
                0%, 20% { content: " ."; }
                40% { content: " .."; }
                60% { content: " ..."; }
                80%, 100% { content: " ...."; }
              }
            </style>
          </head>
          <body>
            <h1>Portfolio Documentation</h1>
            <p>Loading documentation<span class="loading"></span></p>
            <p>If you are not redirected automatically, <a href="/docs/">click here</a>.</p>
          </body>
          </html>
          EOF
