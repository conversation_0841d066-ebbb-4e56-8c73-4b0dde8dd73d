<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Authentication Callback</title>
  <!-- Load runtime configuration -->
  <script src="/runtime-config.js"></script>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100vh;
      margin: 0;
      background-color: #f5f5f5;
      color: #333;
    }
    .container {
      text-align: center;
      padding: 2rem;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      max-width: 500px;
    }
    h1 {
      margin-top: 0;
    }
    .spinner {
      border: 4px solid rgba(0, 0, 0, 0.1);
      border-radius: 50%;
      border-top: 4px solid #3498db;
      width: 40px;
      height: 40px;
      margin: 20px auto;
      animation: spin 1s linear infinite;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Authentication Complete</h1>
    <div class="spinner"></div>
    <p>Authentication was successful. This window will close automatically.</p>
  </div>

  <script>
    // Function to parse URL parameters
    function getUrlParams() {
      const params = {};
      const queryString = window.location.search.substring(1);
      const pairs = queryString.split('&');

      for (const pair of pairs) {
        const [key, value] = pair.split('=');
        if (key) {
          params[decodeURIComponent(key)] = decodeURIComponent(value || '');
        }
      }

      return params;
    }

    // Function to check if we're in a popup window
    function isPopup() {
      return window.opener && window.opener !== window;
    }

    // Function to check the session status
    async function checkSession() {
      try {
        console.log('Checking session status...');

        // Get the auth server URL from runtime config or default to the current origin
        const authServerUrl = (window.runtimeConfig && window.runtimeConfig.AUTH_SERVER_URL) ||
                             'https://my-oauth-proxy.vercel.app';

        console.log('Using auth server URL:', authServerUrl);

        // Try to fetch the session from the auth server
        const response = await fetch(`${authServerUrl}/api/auth/session`, {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Accept': 'application/json',
          },
        });

        console.log('Session response status:', response.status);

        if (response.ok) {
          const text = await response.text();
          console.log('Session response text:', text);

          try {
            const data = JSON.parse(text);
            console.log('Session data:', data);
            return data && data.user;
          } catch (e) {
            console.error('Error parsing session response:', e);
            return false;
          }
        } else {
          console.error('Error fetching session:', response.status, response.statusText);
          return false;
        }
      } catch (error) {
        console.error('Error checking session:', error);
        return false;
      }
    }

    // Function to notify the parent window and close this popup
    async function handleAuthCallback() {
      try {
        const params = getUrlParams();

        // Check for error in URL parameters
        if (params.error) {
          console.error('Authentication error:', params.error);
          document.querySelector('.container p').textContent = `Authentication error: ${params.error}`;
          return;
        }

        // Check session status
        const isAuthenticated = await checkSession();

        if (isAuthenticated) {
          console.log('Authentication successful');
          document.querySelector('.container p').textContent = 'Authentication successful! Redirecting...';

          // Get the redirect path from sessionStorage or default to home
          const redirectPath = sessionStorage.getItem('auth_redirect') || '/';
          console.log('Redirect path:', redirectPath);

          // Check if we have a parent window
          if (isPopup()) {
            console.log('Notifying parent window of successful authentication');

            // Send a message to the parent window
            window.opener.postMessage({
              type: 'auth-success',
              timestamp: new Date().toISOString(),
              redirectPath: redirectPath
            }, '*');

            // Close this window after a short delay
            setTimeout(() => {
              window.close();
            }, 1000);
          } else {
            console.log('Not in a popup window, redirecting to:', redirectPath);
            // If not in a popup, redirect to the stored path
            setTimeout(() => {
              window.location.href = redirectPath;
            }, 1000);
          }
        } else {
          console.error('Authentication failed - no session found');
          document.querySelector('.container p').textContent = 'Authentication failed. Please try again.';

          // Redirect to sign-in page after a delay
          setTimeout(() => {
            window.location.href = '/signin';
          }, 2000);
        }
      } catch (error) {
        console.error('Error handling auth callback:', error);
        document.querySelector('.container p').textContent = `Error: ${error.message}`;
      }
    }

    // Execute when the page loads
    window.onload = function() {
      // Wait a moment to ensure the auth process is complete
      setTimeout(handleAuthCallback, 500);
    };
  </script>
</body>
</html>
